/* Enhanced Styles for TechMart */

/* CSS Variables for Enhanced Theme */
:root {
    --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --secondary-gradient: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
    --accent-gradient: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    --dark-bg: #0f172a;
    --dark-surface: #1e293b;
    --dark-text: #f1f5f9;
    --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-colored: 0 10px 25px -3px rgba(99, 102, 241, 0.3);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --main_color: #667eea;
    --p_color: #a1a1aa;
    --bg_color: #2d2d2d;
    --white_color: #1a1a1a;
    --color_heading: #e5e5e5;
    --border_color: #404040;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Theme Toggle */
.theme-toggle {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-medium);
    z-index: 1000;
    transition: var(--transition);
}

.theme-toggle:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow-heavy);
}

.theme-toggle i {
    color: white;
    font-size: 20px;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 20px;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
}

/* Notification Toast */
.notification-toast {
    position: fixed;
    top: 100px;
    right: 20px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    padding: 16px 20px;
    z-index: 1001;
    transform: translateX(400px);
    transition: var(--transition);
    min-width: 300px;
}

.notification-toast.show {
    transform: translateX(0);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.toast-content i {
    color: var(--success-color);
    font-size: 20px;
}

/* Enhanced Search */
.search-input-container {
    position: relative;
    flex: 1;
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--border_color);
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-suggestion {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid var(--border_color);
    transition: var(--transition);
}

.search-suggestion:hover {
    background: var(--bg_color);
}

.search-suggestion:last-child {
    border-bottom: none;
}

.clear-search {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--p_color);
    cursor: pointer;
    padding: 5px;
    display: none;
}

.clear-search.visible {
    display: block;
}

/* Enhanced Header Icons */
.header_icons .icon {
    position: relative;
}

.user-account .account-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--border_color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: 1000;
}

.user-account:hover .account-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    color: var(--color_heading);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-item:hover {
    background: var(--bg_color);
}

.dropdown-divider {
    margin: 8px 0;
    border: none;
    border-top: 1px solid var(--border_color);
}

/* Cart Preview */
.cart-preview {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--border_color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    width: 350px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: 1000;
}

.cart-icon:hover .cart-preview {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.cart-preview-header {
    padding: 16px;
    border-bottom: 1px solid var(--border_color);
}

.cart-preview-items {
    max-height: 200px;
    overflow-y: auto;
    padding: 16px;
}

.cart-preview-footer {
    padding: 16px;
    border-top: 1px solid var(--border_color);
}

.view-cart-btn {
    width: 100%;
    text-align: center;
}

/* Breadcrumb */
.breadcrumb {
    background: var(--bg_color);
    padding: 12px 0;
    border-bottom: 1px solid var(--border_color);
}

.breadcrumb-list {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    padding: 0;
    list-style: none;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin-left: 8px;
    color: var(--p_color);
}

.breadcrumb-item a {
    color: var(--main_color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: var(--p_color);
}

/* Product Filters */
.product-filters {
    background: white;
    border: 1px solid var(--border_color);
    border-radius: var(--border-radius);
    margin: 30px 0;
    overflow: hidden;
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: var(--bg_color);
    border-bottom: 1px solid var(--border_color);
}

.filter-toggle {
    background: var(--main_color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.filter-toggle:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.filter-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.filter-content.active {
    max-height: 500px;
}

.filter-group h4 {
    margin-bottom: 12px;
    color: var(--color_heading);
    font-weight: 600;
}

.price-range {
    position: relative;
}

.price-slider {
    width: 100%;
    margin: 10px 0;
    -webkit-appearance: none;
    appearance: none;
    height: 6px;
    background: var(--bg_color);
    border-radius: 3px;
    outline: none;
}

.price-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: var(--main_color);
    border-radius: 50%;
    cursor: pointer;
}

.price-values {
    display: flex;
    justify-content: space-between;
    font-weight: 600;
    color: var(--main_color);
}

.filter-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-option {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.filter-option:hover {
    color: var(--main_color);
}

.sort-select {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border_color);
    border-radius: 6px;
    background: white;
    font-size: 14px;
}

.filter-actions {
    display: flex;
    gap: 10px;
    grid-column: 1 / -1;
}

.apply-filters, .clear-filters {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
}

.apply-filters {
    background: var(--main_color);
    color: white;
}

.clear-filters {
    background: var(--bg_color);
    color: var(--color_heading);
    border: 1px solid var(--border_color);
}

.apply-filters:hover, .clear-filters:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Enhanced Product Cards */
.product {
    transition: var(--transition);
    overflow: hidden;
}

.product-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    align-items: center;
}

.btn-wishlist, .btn-compare {
    width: 40px;
    height: 40px;
    border: 1px solid var(--border_color);
    background: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--p_color);
}

.btn-wishlist:hover, .btn-compare:hover {
    background: var(--main_color);
    color: white;
    border-color: var(--main_color);
    transform: translateY(-2px);
}

.btn-wishlist.active {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);
}

.btn-compare.active {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.btn_add_cart {
    flex: 1;
}

/* Modals */
.product-modal, .wishlist-modal, .compare-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1001;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-modal.show, .wishlist-modal.show, .compare-modal.show {
    opacity: 1;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.product-modal.show .modal-content,
.wishlist-modal.show .modal-content,
.compare-modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border_color);
}

.modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 24px;
    cursor: pointer;
    color: var(--p_color);
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--error-color);
}

/* Wishlist Modal */
.wishlist-modal .modal-content {
    width: 800px;
}

.wishlist-items {
    padding: 20px;
}

.wishlist-item {
    display: flex;
    gap: 16px;
    padding: 16px;
    border: 1px solid var(--border_color);
    border-radius: 8px;
    margin-bottom: 16px;
    transition: var(--transition);
}

.wishlist-item:hover {
    box-shadow: var(--shadow-medium);
}

.wishlist-item-image {
    width: 80px;
    height: 80px;
    flex-shrink: 0;
}

.wishlist-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
}

.wishlist-item-details {
    flex: 1;
}

.wishlist-item-details h4 {
    margin-bottom: 8px;
    font-size: 16px;
}

.wishlist-item-price {
    margin-bottom: 12px;
}

.current-price {
    font-weight: 600;
    color: var(--main_color);
    font-size: 18px;
}

.old-price {
    text-decoration: line-through;
    color: var(--p_color);
    margin-left: 8px;
}

.wishlist-item-actions {
    display: flex;
    gap: 8px;
}

.btn-add-to-cart {
    flex: 1;
}

.btn-remove {
    width: 40px;
    height: 40px;
    background: var(--error-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.btn-remove:hover {
    background: #dc2626;
    transform: translateY(-2px);
}

.empty-wishlist, .empty-compare, .single-compare {
    text-align: center;
    padding: 40px;
}

/* Compare Modal */
.compare-modal .modal-content {
    width: 95vw;
    max-width: 1200px;
}

.comparison-table {
    display: grid;
    grid-template-columns: 200px repeat(auto-fit, minmax(200px, 1fr));
    gap: 1px;
    background: var(--border_color);
    border-radius: 8px;
    overflow: hidden;
    margin: 20px;
}

.comparison-header, .comparison-row, .comparison-actions {
    display: contents;
}

.feature-column, .product-column, .feature-cell, .product-cell {
    background: white;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.feature-cell {
    background: var(--bg_color);
    font-weight: 600;
    justify-content: flex-start;
    text-align: left;
}

.product-header {
    flex-direction: column;
    gap: 8px;
    position: relative;
}

.product-header img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 6px;
}

.product-header h5 {
    font-size: 14px;
    margin: 0;
}

.product-header .btn-remove {
    position: absolute;
    top: 0;
    right: 0;
    width: 24px;
    height: 24px;
    font-size: 12px;
}

.price-value {
    font-weight: 600;
    color: var(--main_color);
    font-size: 18px;
}

.savings {
    color: var(--success-color);
    font-weight: 600;
}

.star-rating {
    color: #fbbf24;
}

/* No Results */
.no-results {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    text-align: center;
}

.no-results-content h3 {
    margin-bottom: 12px;
    color: var(--color_heading);
}

.no-results-content p {
    margin-bottom: 20px;
    color: var(--p_color);
}

/* Ripple Effect */
.btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Enhanced Footer */
.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: var(--p_color);
    font-size: 14px;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--main_color);
}

.payment-security {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.security-badges {
    display: flex;
    gap: 16px;
}

.security-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--success-color);
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .filter-content {
        grid-template-columns: 1fr;
    }

    .comparison-table {
        grid-template-columns: 150px repeat(auto-fit, minmax(150px, 1fr));
        margin: 10px;
    }

    .wishlist-modal .modal-content,
    .compare-modal .modal-content {
        width: 95vw;
        margin: 20px;
    }

    .wishlist-item {
        flex-direction: column;
        text-align: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }

    .payment-security {
        flex-direction: column;
        gap: 16px;
    }

    .theme-toggle {
        right: 10px;
        width: 40px;
        height: 40px;
    }

    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
    }
}
