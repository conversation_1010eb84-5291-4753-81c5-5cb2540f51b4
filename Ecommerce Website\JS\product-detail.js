// Product Detail Page Functionality

let currentProduct = null;
let currentQuantity = 1;

// Initialize product detail page
document.addEventListener('DOMContentLoaded', function() {
    loadProductDetail();
    initializeProductDetail();
});

function loadProductDetail() {
    // Get product ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const productId = urlParams.get('id');
    
    if (!productId) {
        // Redirect to home if no product ID
        window.location.href = 'index.html';
        return;
    }
    
    // Load product data
    fetch('products.json')
        .then(response => response.json())
        .then(products => {
            currentProduct = products.find(p => p.id == productId);
            if (currentProduct) {
                renderProductDetail();
                loadRelatedProducts(products);
            } else {
                showProductNotFound();
            }
        })
        .catch(error => {
            console.error('Error loading product:', error);
            showProductNotFound();
        });
}

function renderProductDetail() {
    const container = document.getElementById('product-detail-content');
    
    // Update breadcrumb
    updateBreadcrumb();
    
    // Update page title
    document.title = `${currentProduct.name} | TechMart`;
    
    // Calculate discount percentage
    const discountPercent = currentProduct.old_price ? 
        Math.round(((currentProduct.old_price - currentProduct.price) / currentProduct.old_price) * 100) : 0;
    
    container.innerHTML = `
        <div class="product-images">
            <div class="main-image">
                <img src="${currentProduct.img}" alt="${currentProduct.name}" id="main-product-image">
                <button class="image-zoom" onclick="openImageZoom()">
                    <i class="fas fa-search-plus"></i>
                </button>
            </div>
            <div class="thumbnail-images">
                <div class="thumbnail active" onclick="changeMainImage('${currentProduct.img}', this)">
                    <img src="${currentProduct.img}" alt="Product Image 1">
                </div>
                <!-- Additional thumbnails would go here -->
            </div>
        </div>
        
        <div class="product-info">
            <h1 class="product-title">${currentProduct.name}</h1>
            
            <div class="product-rating">
                <div class="stars">
                    ${generateStarRating(currentProduct.rating || 4)}
                </div>
                <span class="rating-text">(${currentProduct.reviews || 0} reviews)</span>
            </div>
            
            <div class="product-price">
                <span class="current-price">$${currentProduct.price}</span>
                ${currentProduct.old_price ? `
                    <span class="original-price">$${currentProduct.old_price}</span>
                    <span class="discount-badge">${discountPercent}% OFF</span>
                ` : ''}
            </div>
            
            <div class="product-description">
                <p>${currentProduct.description || 'High-quality product with excellent features and performance.'}</p>
            </div>
            
            ${currentProduct.features ? `
                <div class="product-features">
                    <h4>Key Features</h4>
                    <div class="features-list">
                        ${currentProduct.features.map(feature => `
                            <span class="feature-tag">${feature}</span>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
            
            <div class="stock-status ${currentProduct.inStock ? 'in-stock' : 'out-of-stock'}">
                <i class="fas ${currentProduct.inStock ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                <span>${currentProduct.inStock ? 'In Stock' : 'Out of Stock'}</span>
            </div>
            
            <div class="quantity-selector">
                <label>Quantity:</label>
                <div class="quantity-controls">
                    <button class="quantity-btn" onclick="decreaseQuantity()">
                        <i class="fas fa-minus"></i>
                    </button>
                    <input type="number" class="quantity-input" id="quantity-input" value="1" min="1" max="10">
                    <button class="quantity-btn" onclick="increaseQuantity()">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            
            <div class="product-actions">
                <button class="btn-primary" onclick="addToCartFromDetail()" ${!currentProduct.inStock ? 'disabled' : ''}>
                    <i class="fas fa-cart-plus"></i>
                    ${currentProduct.inStock ? 'Add to Cart' : 'Out of Stock'}
                </button>
                <button class="btn-secondary" onclick="addToWishlistFromDetail()">
                    <i class="far fa-heart"></i>
                    Wishlist
                </button>
                <button class="btn-secondary" onclick="addToCompareFromDetail()">
                    <i class="fas fa-balance-scale"></i>
                    Compare
                </button>
            </div>
            
            <div class="product-meta">
                <div class="meta-item">
                    <span class="meta-label">Brand:</span>
                    <span class="meta-value">${currentProduct.brand || 'Generic'}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Category:</span>
                    <span class="meta-value">${currentProduct.catetory}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">SKU:</span>
                    <span class="meta-value">TM-${currentProduct.id.toString().padStart(4, '0')}</span>
                </div>
            </div>
        </div>
    `;
    
    // Add product tabs
    addProductTabs();
}

function addProductTabs() {
    const container = document.getElementById('product-detail-content');
    
    const tabsHTML = `
        <div class="product-tabs">
            <div class="tab-buttons">
                <button class="tab-button active" onclick="switchTab('description', this)">Description</button>
                <button class="tab-button" onclick="switchTab('specifications', this)">Specifications</button>
                <button class="tab-button" onclick="switchTab('reviews', this)">Reviews (${currentProduct.reviews || 0})</button>
            </div>
            
            <div class="tab-content active" id="description-tab">
                <h3>Product Description</h3>
                <p>${currentProduct.description || 'This is a high-quality product designed to meet your needs with excellent performance and reliability.'}</p>
                ${currentProduct.features ? `
                    <h4>Features:</h4>
                    <ul>
                        ${currentProduct.features.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                ` : ''}
            </div>
            
            <div class="tab-content" id="specifications-tab">
                <h3>Specifications</h3>
                <table class="specs-table">
                    <tr><td>Brand</td><td>${currentProduct.brand || 'Generic'}</td></tr>
                    <tr><td>Category</td><td>${currentProduct.catetory}</td></tr>
                    <tr><td>Model</td><td>TM-${currentProduct.id.toString().padStart(4, '0')}</td></tr>
                    <tr><td>Weight</td><td>2.5 kg</td></tr>
                    <tr><td>Warranty</td><td>1 Year</td></tr>
                </table>
            </div>
            
            <div class="tab-content" id="reviews-tab">
                <div class="reviews-summary">
                    <div class="rating-overview">
                        <div class="average-rating">${currentProduct.rating || 4.0}</div>
                        <div class="stars">
                            ${generateStarRating(currentProduct.rating || 4)}
                        </div>
                        <p>${currentProduct.reviews || 0} Reviews</p>
                    </div>
                    
                    <div class="rating-bars">
                        <div class="rating-bar">
                            <span>5★</span>
                            <div class="bar"><div class="bar-fill" style="width: 60%"></div></div>
                            <span>60%</span>
                        </div>
                        <div class="rating-bar">
                            <span>4★</span>
                            <div class="bar"><div class="bar-fill" style="width: 25%"></div></div>
                            <span>25%</span>
                        </div>
                        <div class="rating-bar">
                            <span>3★</span>
                            <div class="bar"><div class="bar-fill" style="width: 10%"></div></div>
                            <span>10%</span>
                        </div>
                        <div class="rating-bar">
                            <span>2★</span>
                            <div class="bar"><div class="bar-fill" style="width: 3%"></div></div>
                            <span>3%</span>
                        </div>
                        <div class="rating-bar">
                            <span>1★</span>
                            <div class="bar"><div class="bar-fill" style="width: 2%"></div></div>
                            <span>2%</span>
                        </div>
                    </div>
                </div>
                
                <div class="reviews-list">
                    ${generateSampleReviews()}
                </div>
            </div>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', tabsHTML);
}

function generateSampleReviews() {
    const sampleReviews = [
        {
            name: "John D.",
            rating: 5,
            date: "2024-01-15",
            text: "Excellent product! Works exactly as described and arrived quickly."
        },
        {
            name: "Sarah M.",
            rating: 4,
            date: "2024-01-10",
            text: "Good quality product. Minor issues with setup but overall satisfied."
        },
        {
            name: "Mike R.",
            rating: 5,
            date: "2024-01-05",
            text: "Outstanding value for money. Highly recommended!"
        }
    ];
    
    return sampleReviews.map(review => `
        <div class="review-item">
            <div class="review-header">
                <div>
                    <div class="reviewer-name">${review.name}</div>
                    <div class="stars">
                        ${generateStarRating(review.rating)}
                    </div>
                </div>
                <div class="review-date">${review.date}</div>
            </div>
            <div class="review-text">${review.text}</div>
        </div>
    `).join('');
}

function generateStarRating(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    
    let stars = '';
    for (let i = 0; i < fullStars; i++) {
        stars += '<i class="fas fa-star"></i>';
    }
    if (hasHalfStar) {
        stars += '<i class="fas fa-star-half-alt"></i>';
    }
    for (let i = 0; i < emptyStars; i++) {
        stars += '<i class="far fa-star"></i>';
    }
    
    return stars;
}

function updateBreadcrumb() {
    const categoryBreadcrumb = document.getElementById('category-breadcrumb');
    const productBreadcrumb = document.getElementById('product-breadcrumb');
    
    if (categoryBreadcrumb) {
        categoryBreadcrumb.textContent = currentProduct.catetory.charAt(0).toUpperCase() + currentProduct.catetory.slice(1);
    }
    
    if (productBreadcrumb) {
        productBreadcrumb.textContent = currentProduct.name.substring(0, 50) + (currentProduct.name.length > 50 ? '...' : '');
    }
}

function initializeProductDetail() {
    // Initialize quantity input
    const quantityInput = document.getElementById('quantity-input');
    if (quantityInput) {
        quantityInput.addEventListener('change', function() {
            currentQuantity = parseInt(this.value) || 1;
            if (currentQuantity < 1) {
                currentQuantity = 1;
                this.value = 1;
            }
        });
    }
}

function changeMainImage(imageSrc, thumbnail) {
    const mainImage = document.getElementById('main-product-image');
    if (mainImage) {
        mainImage.src = imageSrc;
    }
    
    // Update active thumbnail
    document.querySelectorAll('.thumbnail').forEach(thumb => thumb.classList.remove('active'));
    thumbnail.classList.add('active');
}

function increaseQuantity() {
    const input = document.getElementById('quantity-input');
    if (input) {
        currentQuantity = Math.min(parseInt(input.value) + 1, 10);
        input.value = currentQuantity;
    }
}

function decreaseQuantity() {
    const input = document.getElementById('quantity-input');
    if (input) {
        currentQuantity = Math.max(parseInt(input.value) - 1, 1);
        input.value = currentQuantity;
    }
}

function addToCartFromDetail() {
    if (!currentProduct || !currentProduct.inStock) return;
    
    // Add multiple quantities
    for (let i = 0; i < currentQuantity; i++) {
        if (typeof addToCart === 'function') {
            addToCart(currentProduct);
        }
    }
    
    showNotification(`Added ${currentQuantity} item(s) to cart!`, 'success');
}

function addToWishlistFromDetail() {
    if (!currentProduct) return;
    
    if (typeof addToWishlist === 'function') {
        addToWishlist(currentProduct.id);
    }
}

function addToCompareFromDetail() {
    if (!currentProduct) return;
    
    if (typeof addToCompare === 'function') {
        addToCompare(currentProduct.id);
    }
}

function switchTab(tabName, button) {
    // Remove active class from all tabs and buttons
    document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    
    // Add active class to clicked button and corresponding content
    button.classList.add('active');
    document.getElementById(tabName + '-tab').classList.add('active');
}

function openImageZoom() {
    // Create image zoom modal
    const modal = document.createElement('div');
    modal.className = 'image-zoom-modal';
    modal.innerHTML = `
        <div class="zoom-modal-content">
            <span class="zoom-close">&times;</span>
            <img src="${currentProduct.img}" alt="${currentProduct.name}">
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal functionality
    modal.querySelector('.zoom-close').onclick = () => modal.remove();
    modal.onclick = (e) => {
        if (e.target === modal) modal.remove();
    };
}

function loadRelatedProducts(allProducts) {
    const relatedProducts = allProducts
        .filter(p => p.catetory === currentProduct.catetory && p.id !== currentProduct.id)
        .slice(0, 4);
    
    const container = document.getElementById('related-products-grid');
    
    container.innerHTML = relatedProducts.map(product => `
        <div class="product" data-aos="fade-up">
            <div class="img_product">
                <img src="${product.img}" alt="${product.name}">
            </div>
            <h3 class="name_product">
                <a href="product-detail.html?id=${product.id}">${product.name}</a>
            </h3>
            <div class="stars">
                ${generateStarRating(product.rating || 4)}
            </div>
            <div class="price">
                <p><span>$${product.price}</span></p>
            </div>
            <button class="btn_add_cart btn" onclick="quickAddToCart(${product.id})">
                <i class="fa-solid fa-cart-shopping"></i> Add to Cart
            </button>
        </div>
    `).join('');
}

function quickAddToCart(productId) {
    fetch('products.json')
        .then(response => response.json())
        .then(products => {
            const product = products.find(p => p.id === productId);
            if (product && typeof addToCart === 'function') {
                addToCart(product);
                showNotification('Added to cart!', 'success');
            }
        });
}

function showProductNotFound() {
    const container = document.getElementById('product-detail-content');
    container.innerHTML = `
        <div class="product-not-found">
            <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: var(--error-color); margin-bottom: 20px;"></i>
            <h2>Product Not Found</h2>
            <p>The product you're looking for doesn't exist or has been removed.</p>
            <a href="index.html" class="btn">Back to Home</a>
        </div>
    `;
}
