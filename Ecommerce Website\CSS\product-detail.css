/* Product Detail Page Styles */

.product-detail {
    padding: 40px 0;
    background: white;
}

.product-detail-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
}

/* Product Images */
.product-images {
    position: relative;
}

.main-image {
    width: 100%;
    height: 400px;
    border: 1px solid var(--border_color);
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: 20px;
    position: relative;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.main-image:hover img {
    transform: scale(1.1);
}

.image-zoom {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.image-zoom:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

.thumbnail-images {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    padding: 10px 0;
}

.thumbnail {
    width: 80px;
    height: 80px;
    border: 2px solid transparent;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition);
    flex-shrink: 0;
}

.thumbnail.active {
    border-color: var(--main_color);
}

.thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.thumbnail:hover {
    border-color: var(--main_color);
    transform: scale(1.05);
}

/* Product Info */
.product-info {
    padding: 20px 0;
}

.product-title {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--color_heading);
    line-height: 1.3;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.stars {
    display: flex;
    gap: 2px;
    color: #fbbf24;
}

.rating-text {
    color: var(--p_color);
    font-size: 14px;
}

.product-price {
    margin-bottom: 24px;
}

.current-price {
    font-size: 32px;
    font-weight: 700;
    color: var(--main_color);
}

.original-price {
    font-size: 20px;
    color: var(--p_color);
    text-decoration: line-through;
    margin-left: 12px;
}

.discount-badge {
    background: var(--error-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 12px;
}

.product-description {
    margin-bottom: 24px;
    line-height: 1.6;
    color: var(--p_color);
}

.product-features {
    margin-bottom: 24px;
}

.product-features h4 {
    margin-bottom: 12px;
    color: var(--color_heading);
}

.features-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.feature-tag {
    background: var(--bg_color);
    color: var(--color_heading);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    border: 1px solid var(--border_color);
}

.stock-status {
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.stock-status.in-stock {
    color: var(--success-color);
}

.stock-status.out-of-stock {
    color: var(--error-color);
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;
}

.quantity-controls {
    display: flex;
    align-items: center;
    border: 1px solid var(--border_color);
    border-radius: 6px;
    overflow: hidden;
}

.quantity-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--bg_color);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.quantity-btn:hover {
    background: var(--main_color);
    color: white;
}

.quantity-input {
    width: 60px;
    height: 40px;
    border: none;
    text-align: center;
    font-weight: 600;
    background: white;
}

.product-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 32px;
}

.btn-primary {
    flex: 2;
    background: var(--main_color);
    color: white;
    border: none;
    padding: 16px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary:hover {
    background: #e26e02;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    flex: 1;
    background: white;
    color: var(--main_color);
    border: 2px solid var(--main_color);
    padding: 16px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-secondary:hover {
    background: var(--main_color);
    color: white;
    transform: translateY(-2px);
}

.product-meta {
    border-top: 1px solid var(--border_color);
    padding-top: 24px;
}

.meta-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border_color);
}

.meta-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.meta-label {
    font-weight: 600;
    color: var(--color_heading);
}

.meta-value {
    color: var(--p_color);
}

/* Product Tabs */
.product-tabs {
    margin-top: 40px;
    border-top: 1px solid var(--border_color);
    padding-top: 40px;
}

.tab-buttons {
    display: flex;
    gap: 0;
    margin-bottom: 24px;
    border-bottom: 1px solid var(--border_color);
}

.tab-button {
    padding: 16px 24px;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 600;
    color: var(--p_color);
    border-bottom: 3px solid transparent;
    transition: var(--transition);
}

.tab-button.active {
    color: var(--main_color);
    border-bottom-color: var(--main_color);
}

.tab-button:hover {
    color: var(--main_color);
}

.tab-content {
    display: none;
    padding: 24px 0;
}

.tab-content.active {
    display: block;
}

/* Reviews */
.reviews-summary {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 32px;
    margin-bottom: 32px;
}

.rating-overview {
    text-align: center;
    padding: 24px;
    background: var(--bg_color);
    border-radius: var(--border-radius);
}

.average-rating {
    font-size: 48px;
    font-weight: 700;
    color: var(--main_color);
    margin-bottom: 8px;
}

.rating-bars {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.rating-bar {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.bar {
    flex: 1;
    height: 8px;
    background: var(--border_color);
    border-radius: 4px;
    overflow: hidden;
}

.bar-fill {
    height: 100%;
    background: var(--main_color);
    transition: width 0.3s ease;
}

.review-item {
    border: 1px solid var(--border_color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 16px;
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.reviewer-name {
    font-weight: 600;
    color: var(--color_heading);
}

.review-date {
    color: var(--p_color);
    font-size: 14px;
}

.review-text {
    line-height: 1.6;
    color: var(--p_color);
}

/* Related Products */
.related-products {
    padding: 40px 0;
    background: var(--bg_color);
}

.related-products h2 {
    margin-bottom: 32px;
    text-align: center;
}

.related-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .product-detail-content {
        grid-template-columns: 1fr;
        gap: 24px;
    }
    
    .product-title {
        font-size: 24px;
    }
    
    .current-price {
        font-size: 28px;
    }
    
    .product-actions {
        flex-direction: column;
    }
    
    .reviews-summary {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .tab-buttons {
        flex-wrap: wrap;
    }
    
    .tab-button {
        flex: 1;
        min-width: 120px;
    }
    
    .related-products-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
    }
}
