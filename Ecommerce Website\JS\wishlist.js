// Wishlist Functionality

let wishlistProducts = [];

// Initialize wishlist
document.addEventListener('DOMContentLoaded', function() {
    initializeWishlist();
    loadWishlist();
});

function initializeWishlist() {
    const wishlistIcon = document.getElementById('wishlist-icon');
    const wishlistModal = document.getElementById('wishlist-modal');
    
    if (wishlistIcon) {
        wishlistIcon.addEventListener('click', function() {
            openWishlistModal();
        });
    }
    
    // Close modal functionality
    if (wishlistModal) {
        const closeBtn = wishlistModal.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', closeWishlistModal);
        }
        
        wishlistModal.addEventListener('click', function(e) {
            if (e.target === wishlistModal) {
                closeWishlistModal();
            }
        });
    }
    
    // Add wishlist buttons to existing products
    addWishlistButtonsToProducts();
}

function loadWishlist() {
    const wishlistIds = JSON.parse(localStorage.getItem('wishlist')) || [];
    
    if (typeof allProducts !== 'undefined' && allProducts.length > 0) {
        updateWishlistProducts(wishlistIds);
    } else {
        // Load products if not already loaded
        fetch('products.json')
            .then(response => response.json())
            .then(data => {
                if (typeof allProducts === 'undefined') {
                    window.allProducts = data;
                }
                updateWishlistProducts(wishlistIds);
            })
            .catch(error => {
                console.error('Error loading products for wishlist:', error);
            });
    }
}

function updateWishlistProducts(wishlistIds) {
    wishlistProducts = allProducts.filter(product => 
        wishlistIds.includes(product.id)
    );
    updateWishlistCount();
    updateWishlistButtons();
}

function addToWishlist(productId) {
    let wishlistIds = JSON.parse(localStorage.getItem('wishlist')) || [];
    
    if (!wishlistIds.includes(productId)) {
        wishlistIds.push(productId);
        localStorage.setItem('wishlist', JSON.stringify(wishlistIds));
        
        const product = allProducts.find(p => p.id === productId);
        if (product) {
            wishlistProducts.push(product);
        }
        
        updateWishlistCount();
        updateWishlistButtons();
        showNotification('Added to wishlist!', 'success');
        
        // Update button state
        const buttons = document.querySelectorAll(`[data-product-id="${productId}"] .btn-wishlist`);
        buttons.forEach(btn => {
            btn.classList.add('active');
            btn.innerHTML = '<i class="fas fa-heart"></i>';
            btn.title = 'Remove from Wishlist';
        });
    } else {
        removeFromWishlist(productId);
    }
}

function removeFromWishlist(productId) {
    let wishlistIds = JSON.parse(localStorage.getItem('wishlist')) || [];
    wishlistIds = wishlistIds.filter(id => id !== productId);
    localStorage.setItem('wishlist', JSON.stringify(wishlistIds));
    
    wishlistProducts = wishlistProducts.filter(product => product.id !== productId);
    
    updateWishlistCount();
    updateWishlistButtons();
    updateWishlistModal();
    showNotification('Removed from wishlist!', 'info');
    
    // Update button state
    const buttons = document.querySelectorAll(`[data-product-id="${productId}"] .btn-wishlist`);
    buttons.forEach(btn => {
        btn.classList.remove('active');
        btn.innerHTML = '<i class="fa-regular fa-heart"></i>';
        btn.title = 'Add to Wishlist';
    });
}

function updateWishlistCount() {
    const countElements = document.querySelectorAll('.count_favourite');
    countElements.forEach(el => {
        el.textContent = wishlistProducts.length;
    });
}

function updateWishlistButtons() {
    const wishlistIds = wishlistProducts.map(p => p.id);
    
    document.querySelectorAll('.btn-wishlist').forEach(btn => {
        const productCard = btn.closest('[data-product-id]');
        if (productCard) {
            const productId = parseInt(productCard.dataset.productId);
            
            if (wishlistIds.includes(productId)) {
                btn.classList.add('active');
                btn.innerHTML = '<i class="fas fa-heart"></i>';
                btn.title = 'Remove from Wishlist';
            } else {
                btn.classList.remove('active');
                btn.innerHTML = '<i class="fa-regular fa-heart"></i>';
                btn.title = 'Add to Wishlist';
            }
        }
    });
}

function addWishlistButtonsToProducts() {
    // This function adds wishlist buttons to products that don't have them
    const products = document.querySelectorAll('.product');
    
    products.forEach(product => {
        if (!product.querySelector('.btn-wishlist')) {
            const productId = getProductIdFromElement(product);
            if (productId) {
                product.dataset.productId = productId;
                
                const actionsContainer = product.querySelector('.product-actions') || 
                                       createProductActionsContainer(product);
                
                const wishlistBtn = document.createElement('button');
                wishlistBtn.className = 'btn-wishlist';
                wishlistBtn.innerHTML = '<i class="fa-regular fa-heart"></i>';
                wishlistBtn.title = 'Add to Wishlist';
                wishlistBtn.onclick = () => addToWishlist(productId);
                
                actionsContainer.appendChild(wishlistBtn);
            }
        }
    });
}

function createProductActionsContainer(product) {
    const actionsContainer = document.createElement('div');
    actionsContainer.className = 'product-actions';
    
    const addToCartBtn = product.querySelector('.btn_add_cart');
    if (addToCartBtn) {
        addToCartBtn.parentNode.insertBefore(actionsContainer, addToCartBtn.nextSibling);
        actionsContainer.appendChild(addToCartBtn);
    } else {
        product.appendChild(actionsContainer);
    }
    
    return actionsContainer;
}

function getProductIdFromElement(productElement) {
    // Try to get product ID from add to cart button
    const addToCartBtn = productElement.querySelector('.btn_add_cart[data-id]');
    if (addToCartBtn) {
        return parseInt(addToCartBtn.dataset.id);
    }
    
    // Try to get from product element itself
    if (productElement.dataset.productId) {
        return parseInt(productElement.dataset.productId);
    }
    
    return null;
}

function openWishlistModal() {
    const modal = document.getElementById('wishlist-modal');
    if (modal) {
        modal.style.display = 'flex';
        updateWishlistModal();
        
        // Add animation
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }
}

function closeWishlistModal() {
    const modal = document.getElementById('wishlist-modal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }
}

function updateWishlistModal() {
    const wishlistContainer = document.getElementById('wishlist-items');
    if (!wishlistContainer) return;
    
    if (wishlistProducts.length === 0) {
        wishlistContainer.innerHTML = `
            <div class="empty-wishlist">
                <i class="fas fa-heart-broken" style="font-size: 48px; color: var(--p_color); margin-bottom: 20px;"></i>
                <h3>Your wishlist is empty</h3>
                <p>Add some products to your wishlist to see them here</p>
                <button class="btn" onclick="closeWishlistModal()">Continue Shopping</button>
            </div>
        `;
        return;
    }
    
    wishlistContainer.innerHTML = wishlistProducts.map(product => `
        <div class="wishlist-item" data-product-id="${product.id}">
            <div class="wishlist-item-image">
                <img src="${product.img}" alt="${product.name}">
            </div>
            <div class="wishlist-item-details">
                <h4>${product.name}</h4>
                <div class="wishlist-item-price">
                    <span class="current-price">$${product.price}</span>
                    ${product.old_price ? `<span class="old-price">$${product.old_price}</span>` : ''}
                </div>
                <div class="wishlist-item-actions">
                    <button class="btn btn-add-to-cart" onclick="addToCartFromWishlist(${product.id})">
                        <i class="fas fa-cart-plus"></i> Add to Cart
                    </button>
                    <button class="btn-remove" onclick="removeFromWishlist(${product.id})" title="Remove from Wishlist">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

function addToCartFromWishlist(productId) {
    const product = allProducts.find(p => p.id === productId);
    if (product && typeof addToCart === 'function') {
        addToCart(product);
        showNotification('Added to cart!', 'success');
        
        // Update add to cart button state
        const buttons = document.querySelectorAll(`.btn_add_cart[data-id="${productId}"]`);
        buttons.forEach(btn => {
            btn.classList.add('active');
            btn.innerHTML = '<i class="fa-solid fa-cart-shopping"></i> Item in cart';
        });
    }
}

function clearWishlist() {
    localStorage.removeItem('wishlist');
    wishlistProducts = [];
    updateWishlistCount();
    updateWishlistButtons();
    updateWishlistModal();
    showNotification('Wishlist cleared!', 'info');
}

function shareWishlist() {
    const wishlistIds = wishlistProducts.map(p => p.id);
    const shareUrl = `${window.location.origin}${window.location.pathname}?wishlist=${wishlistIds.join(',')}`;
    
    if (navigator.share) {
        navigator.share({
            title: 'My TechMart Wishlist',
            text: 'Check out my wishlist on TechMart!',
            url: shareUrl
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(shareUrl).then(() => {
            showNotification('Wishlist link copied to clipboard!', 'success');
        });
    }
}

// Load wishlist from URL parameter (for shared wishlists)
function loadSharedWishlist() {
    const urlParams = new URLSearchParams(window.location.search);
    const sharedWishlist = urlParams.get('wishlist');
    
    if (sharedWishlist) {
        const productIds = sharedWishlist.split(',').map(id => parseInt(id));
        localStorage.setItem('wishlist', JSON.stringify(productIds));
        loadWishlist();
        showNotification('Shared wishlist loaded!', 'success');
    }
}

// Initialize shared wishlist on page load
document.addEventListener('DOMContentLoaded', function() {
    loadSharedWishlist();
});

// Export functions for global use
window.addToWishlist = addToWishlist;
window.removeFromWishlist = removeFromWishlist;
window.addToCartFromWishlist = addToCartFromWishlist;
window.clearWishlist = clearWishlist;
window.shareWishlist = shareWishlist;
