@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

*{
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    list-style: none;
    text-decoration: none;
    font-family: "Inter" , sans-serif;
    color: var(--color_heading);
}
:root{
    --main_color : #6366f1;
    --p_color: #6b7280;
    --bg_color: #f8fafc;
    --white_color: #fff;
    --color_heading: #1f2937;
    --border_color: #e5e7eb;
    --Sale_color: #ef4444;
}


body{
    padding-top: 180px;
}


span{
    color: var(--main_color);
}
p{
    color: var(--p_color);
}
h1,h2,h3,h4,h5,h6{
    color: var(--color_heading);
    font-family: "DM Sans" , sans-serif;
}

img{
    width: 100%;
}

input,select,button{
    border: none;
    outline: none;
}

.btns{
    display: flex;
    align-items: center;
    gap: 20px;
}

.btn{
    padding: 10px 18px;
    text-transform: capitalize;
    border-radius: 2px;
    cursor: pointer;
    background: var(--main_color);
    color: var(--white_color);
    display: flex;
    gap: 10px;
    align-items: center;
    transition: 0.3s;
}
.btn i{
    color: var(--white_color);
}
.btn:hover{
    scale: 1.1;
}

.container{
    width: 90%;
    margin: auto;
    max-width: 1350px;
}

@media (max-width:1350px) {
    .container{
        width: 90%;
    }
}

/* Start Header */

header{
    background: #fff;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    box-shadow: 5px 8px 8px #d1d1d13b;
    z-index: 1000;
}

header .top_header .container{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
}

header .top_header .logo{
    width: 180px;
}

header .top_header .search_box{
    width: 610px;
    display: flex;
    align-items: center;
    border-radius: 2px;
    background: var(--bg_color);
}
header .top_header .search_box input{
    height: 55px;
    width: 400px;
    padding: 5px 15px 5px 10px;
    background: var(--bg_color);
}

header .top_header .search_box .select_box{
    position: relative;
}
header .top_header .search_box .select_box::after{
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 0;
    width: 1px;
    height: 50%;
    background: #b9b9b9;
}


header .top_header .search_box select{
    height: 55px;
    width: 190px;
    background: var(--bg_color);
    font-size: 16px;
    cursor: pointer;
    padding-left: 10px;
    margin-right: 10px;
}

header .top_header .search_box select option{
    font-size: 15px;
}

header .top_header .search_box button{
    height: 55px;
    width: 60px;
    background: var(--main_color);
    font-size: 18px;
    cursor: pointer;
}
header .top_header .search_box button i{
    color: var(--white_color);
}

header .top_header .header_icons{
    display: flex;
    gap: 30px;
}
header .top_header .header_icons .icon{
    position: relative;
    cursor: pointer;
}
header .top_header .header_icons .icon i{
    font-size: 24px;
}

header .top_header .header_icons .icon .count{
    position: absolute;
    top: -10px;
    right: -10px;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    background: var(--main_color);
    color: var(--white_color);
    font-size: 11px;
    border-radius: 100%;
}



header .bottom_header{
    border-top: 1px solid var(--border_color);
}

header .bottom_header .container{
    display: flex;
    align-items: center;
    justify-content: space-between;
}

header .bottom_header nav{
    display: flex;
    align-items: center;
    gap: 50px;
    height: 60px;
}

header .bottom_header .category_nav{
    width: 220px;
    position: relative;
    height: 100%;
}
header .bottom_header .category_nav .category_btn{
    height: 100%;
    width: 100%;
    border-radius: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--main_color);
    padding: 0 15px;
    cursor: pointer;
}
header .bottom_header .category_nav .category_btn p{
    color: var(--white_color);
    font-weight: 600;
    font-size: 15px;
}
header .bottom_header .category_nav .category_btn i{
    color: var(--white_color);
}


header .bottom_header .category_nav .category_nav_list{
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--white_color);
    width: 100%;
    border: 1px solid #999;
    border-top: 0;
    display: flex;
    flex-direction: column;
    clip-path: polygon(0 0 , 100% 0 , 100% 0 , 0 0);
    transition: 0.3s ease-in-out;
}
header .bottom_header .category_nav .category_nav_list.active{
    clip-path: polygon(0 0, 100% 0 , 100% 100% , 0 100%);
}

header .bottom_header .category_nav .category_nav_list a{
    padding: 14px 10px;
    border-bottom: 1px solid var(--border_color);
    font-size: 14px;
}
header .bottom_header .category_nav .category_nav_list a:last-child{
    border-bottom: 0;
}
header .bottom_header .category_nav .category_nav_list a:hover{
    background: #d0d0d0;
}






header .bottom_header .nav_links{
    display: flex;
    gap: 35px;
}
header .bottom_header .nav_links li a{
    color: var(--color_heading);
    transition: 0.3s;
}
header .bottom_header .nav_links li:hover a,
header .bottom_header .nav_links li.active a{
    color: var(--main_color);
}



/* Slide */


.slider{
    position: relative;
}

.slider .container{
    display: flex;
    justify-content: space-between;
}

.slider  .banner_2{
    width: 23%;
    height: 100%;
    object-fit: cover;
}

.slider  .banner_2 a{
    height: 100%;
    width: 100%;
}


.slider .container .slide-swp{
    width: 75%;
    overflow: hidden;
    position: relative;
}

.swiper-wrapper{
    height: auto !important;
}

.slider .container .slide-swp .swiper-pagination span{
    background: #fff;
    opacity: 1;
}

.swiper-pagination-bullet-active{
    background: var(--main_color) !important;
    width: 34px !important;
    height: 8px !important;
    border-radius: 30px !important;
}



/* banners_4 */ 

.banners_4{
    margin: 40px 0;
}

.banners_4 .container{
    display: flex;
    justify-content: space-between;
}

.banners_4 .container .box{
    width: 24%;
    background: url(../img/bg_banner3.jpg);
    background-size: cover;
    background-position: center;
    border-radius: 5px;
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px 10px;
    position: relative;
}

.banners_4 .container .box img{
    width: 100px;
    transition: 0.3s;
}
.banners_4 .container .box:hover img{
    scale: 1.05;
}
.banners_4 .container .box h5{
    font-size: 16px;
}
.banners_4 .container .box .sale{
    display: flex;
    align-items: center;
    gap: 5px;
    margin: 7px 0;

}

.banners_4 .container .box .sale span{
    font-size: 25px;
    font-weight: bold;
}

.banners_4 .container .box h6{
    font-size: 14px;
    font-weight: bold;
}

.banners_4 .container .box .link_btn{
    position: absolute;
    width: 100%;
    height: 100%;
    background: transparent;
}

/* Start Slide */ 

.slide{
    margin-bottom: 70px;
}
.top_slide{
    position: relative;
    margin-bottom: 20px;
    border-bottom: 3px solid var(--main_color);
}

.top_slide h2{
    position: relative;
    text-transform: uppercase;
    background: var(--main_color);
    color: var(--white_color);
    font-size: 18px;
    padding: 10px 40px;
    display: flex;
    align-items: center;
    gap: 10px;
    width: max-content;
}
.top_slide h2::before{
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    top: 0;
    z-index: 9;
    border-left: 20px solid transparent;
    background: #fff;
    left: 0;
    border-bottom: 20px solid #e26e02;
}

.top_slide h2 i{
    color: var(--white_color);
}

.slide .container{
    position: relative;
}
.slide .container .mySwiper{
    padding: 10px 0;
}

.slide .container .mySwiper .btn_Swip{
    position: absolute;
    top: 30px;
    right: 0;
    background: var(--bg_color);
    color: var(--color_heading);
    font-weight: bold;
    border-radius: 5px;
    height: 35px;
    width: 35px;
    border: 1px solid var(--border_color);
}
.slide .container .mySwiper .btn_Swip::after{
    font-size: 12px;
}
.slide .container .mySwiper .btn_Swip.swiper-button-prev{
    left: calc(100% - 80px);
}


/* product card style */ 
.mySwiper{
    overflow: hidden;
}
.product {
    background: #fff;
    padding: 20px 20px;
    box-shadow: 5px 5px 10px #94949428;
    border: 1px solid var(--border_color);
    border-radius: 5px;
    position: relative;
}

.product .sale_present{
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
    background: #ed0505;
    color: var(--white_color);
    padding: 4px 10px;
    font-size: 13px;
}
.product .img_product{
    position: relative;
    height: 180px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.3s;
}
.product:hover .img_product{
    scale: 1.1;
}
.product .name_product{
    margin-bottom: 10px;
    color: var(--color_heading);
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}
.product .name_product a:hover{
    text-decoration: underline;
}

.stars{
    margin-bottom: 10px;
}
.stars i{
    color: var(--main_color);
    font-size: 14px;
}

.price{
    display: flex;
    align-items: center;
    gap: 12px;
}
.price p{
    color: var(--main_color);
    font-weight: bold;
    font-size: 18px;
}
.price .old_price{
    color: var(--p_color);
    text-decoration: line-through;
    font-size: 13px;
    font-weight: normal;
}

.product .icons{
    display: flex;
    gap: 10px;
    margin-top: 15px;
}
.product .icons .icon_product{
    width: 40px;
    height: 40px;
    border: 1px solid var(--border_color);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.3s;
}
.product .icons .icon_product:hover{
    background: var(--main_color);
}
.product .icons .icon_product i{
    font-size: 14px;
}
.product .icons .icon_product:hover i{
    color: var(--white_color);
}
.product .icons  .btn_add_cart{
    background: var(--main_color);
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--white_color);
    padding: 5px 10px;
    cursor: pointer;
    transition: 0.3s;
    border: 1px solid var(--main_color);
}
.product .icons  .btn_add_cart i {
    color: var(--white_color);
    pointer-events: none;
}
.product .icons  .btn_add_cart.active{
    color: var(--color_heading);
    background: transparent;
    pointer-events: none;
}
.product .icons  .btn_add_cart.active i{
    color: var(--main_color);
}
.product .icons  .btn_add_cart:Hover{
    scale: 1.05;
    background: #f47600;
}


/* banners  banners 3*/

.banners{
    margin: 50px 0;
}
.banners .banners_boxs{
    display: flex;
    justify-content: space-between;
}
.banners .banners_boxs .box{
    width: 49%;
}

.banners .banners_boxs.banner_3_img .box{
    width: 32%;
}


/* Start Footer */ 

footer{
    background: var(--color_heading);
}
footer .container{
    display: flex;
    justify-content: space-between;
    padding: 40px 0;
}

footer .container  .big_row{
    width: 30%;
}

footer .container  .big_row .logo_footer{
    width: 180px;
}

footer .container  .big_row p{
    width: 90%;
    color: #b1b1b1;
    font-size: 14px;
    line-height: 1.5;
    margin: 15px 0;
}

footer .container  .big_row .icons_footer{
    display: flex;
    gap: 10px;
}
footer .container  .big_row .icons_footer a{
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--main_color);
    border-radius: 3px;
    position: relative;
    top: 0;
    transition: 0.3s;
}
footer .container  .big_row .icons_footer a:hover{
    top: -5px;
}
footer .container  .big_row .icons_footer a i{
    color: var(--white_color);
    font-size: 20px;
}

footer .container .row{
    width: 22%;
}

footer .container .row h4{
    color: var(--white_color);
    font-size: 18px;
    margin-bottom: 20px;
}
footer .container .row .links{
    display: flex;
    flex-direction: column;
    gap: 7px;
}


footer .container .row .links a{
    color: #b1b1b1;
    font-size: 14px;
    position: relative;
    left: 0;
    transition: 0.3s;
}
footer .container .row .links a i{
    color: var(--main_color);
    font-size: 14px;
    margin-right: 5px;
}
footer .container .row .links a:hover{
    left: 8px;
    color: var(--main_color);
}
footer  .bottom_footer{
    background: #1d1f22;
}
footer  .bottom_footer .container{
    align-items: center;
    padding: 5px 0;
}
footer  .bottom_footer p{
    color: var(--white_color);
}

footer  .bottom_footer .payment_img{
    width: 350px;
}


/* End Footer */


.cart{
    position: fixed;
    top: 0;
    right: -350px;
    bottom: 0;
    z-index: 1100;
    background: var(--white_color);
    border-left: 1px solid var(--border_color);
    width: 350px;
    padding: 30px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: 0.3s ease-in-out;
}
.cart.active{
    right: 0;
}

.cart .top_cart{
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.cart .top_cart .close_cart i{
    cursor: pointer;
    font-size: 35px;
}
.cart .top_cart h3{
    font-size: 18px;
}
.cart .top_cart h3 span{
    color: var(--color_heading);
}


.cart .items_in_cart{
    padding: 20px 0;
    border-block: 1px solid var(--border_color);
    margin-block: 20px;
    height: 100%;
    overflow-y: auto;
}

.cart .items_in_cart .item_cart{
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: center;
    height: 125px;
    border-bottom: 1px solid var(--border_color);
}
.cart .items_in_cart .item_cart:last-child{
    border-bottom: 0;
    margin-bottom: 0;
}
.cart .items_in_cart .item_cart img{
    width: 80px;
}

.cart .items_in_cart .item_cart h4{
    margin-bottom: 10px;
    font-weight: 500;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical
}
.cart .items_in_cart .item_cart .delete_item i{
    font-size: 22px;
    cursor: pointer;
    transition: 0.3s;
}
.cart .items_in_cart .item_cart .delete_item i:hover{
    color: #E51A1A;
}

.cart .items_in_cart .item_cart .quantity_control{
    display: flex;
    align-self: center;
    gap: 5px;
    margin-top: 5px;
}

.cart .items_in_cart .item_cart .quantity_control span{
    font-size: 18px;
    min-width: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.cart .items_in_cart .item_cart .quantity_control  button{
    width: 27px;
    height: 27px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 20px;
    border-radius: 2px;
    border: 1px solid var(--border_color);
}
.cart .bottom_cart .total{
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
}
.cart .bottom_cart .price_cart_toral{
    color: var(--main_color);
    font-size: 20px;
}

.cart .bottom_cart .button_cart{
    display: flex;
    gap: 20px;
}

.cart .bottom_cart .button_cart .btn_cart{
    text-transform: uppercase;
    border: 2px solid var(--main_color);
    cursor: pointer;
    border-radius: 5px;
    transition: 0.3s;
}
.cart .bottom_cart .button_cart .trans_bg{
    background: transparent;

color: var(--color_heading);
}

/* Start Cart */ 




.close_menu,
.open_menu{
    display: none;
}



/* STart Responsive */ 


@media (max-width:1100px) {
    body{
        padding-top: 240px;
    }
    
    header .top_header .container{
        flex-wrap: wrap;
    }
    header .top_header .search_box{
        order: 3;
        width: 80%;
        margin: 20px auto 0;
    }
    header .top_header .logo{
        width: 140px;
    }
    header .top_header .search_box select{
        width: 200px;
    }
    header .top_header .search_box input{
        width: calc(100% - 200px);
    }
    header .bottom_header .nav_links{
        position: fixed;
        top: 0;
        left: -400px;
        bottom: 0;
        background: #fff;
        width: 400px;
        flex-direction: column;
        align-items: center;
        padding-top: 100px;
        border: 1px solid #b5b5b596;
        transition: 0.3s ease-in-out;
    }
    header .bottom_header .nav_links.active{
        left: 0;
    }
    header .bottom_header .nav_links li{
        font-size: 18px;
        height: auto;
    }

    .close_menu{
        display: block;
        position: absolute;
        top: 30px;
        right: 30px;
        font-size: 30px;
    }
    .open_menu{
        display: block;
        font-size: 25px;
    }
    .open_menu i{
        color: var(--main_color);
        border: 1px solid var(--main_color);
        height: 40px;
        width: 40px;
        text-align: center;
        line-height: 40px;
        border-radius: 3px;
    }

    header .bottom_header .category_nav{
        width: auto;
    }
    header .bottom_header .category_nav .category_btn p{
        font-size: 13px;
        margin: 0 10px;
    }
    header .bottom_header nav{
        gap: 20px;
    }





    .banners_4 .container{
        flex-wrap: wrap;
  
    }
    .banners_4 .container .box{
        width: 49%;
        margin-bottom: 20px;
        justify-content: space-around;
    }
}

@media (max-width:1000px) {
    .slider .banner_2{
        display: none;
    }
    .slider .container .slide-swp{
        width: 100%;
    }
  


    footer .container{
        flex-wrap: wrap;
    }
    footer .container .big_row{
        width: 40%;
        margin-bottom: 20px;
    }
    footer .container .row{
        width: 30%;
        margin-bottom: 20px;
    }


}
@media (max-width:800px){

    .top_slide h2{
        font-size: 16px;
        padding: 10px 25px;
    }
    .product .name_product{
        font-size: 14px;
    }
    .price p{
        font-size: 16px;
    }
    .price .old_price{
        font-size: 12px;
    }
    .product .icons .btn_add_cart{
        font-size: 14px;
        padding: 3px 5px;
    }
    .banners .banners_boxs{
        flex-wrap: wrap;
    }
    .banners .banners_boxs .box{
        width: 100%;
        margin-bottom: 20px;
    }
    .banners .banners_boxs.banner_3_img .box{
        width: 49%;
    }
    






    footer .container .big_row{
        width: 50%;
        margin-bottom: 30px;
    }
    footer .container .row{
        width: 50%;
        margin-bottom: 30px;
    }
    footer .bottom_footer .container{
        flex-direction: column;
        gap: 10px;
    }
    

}


@media (max-width:500px) {
    
    header .bottom_header .category_nav{
        display: none;
    }
    header .top_header .search_box{
        width: 100%;
    }
    header .top_header .search_box select{
        width: 140px;
    }
    header .top_header .search_box input{
        width: calc(100% - 140px);
    }
    header .bottom_header .nav_links{
        width: 100%;
        left: -100%;
    }
    .cart{
        width: 100%;
        right: -100%;
    }

    .banners_4 .container .box{
        width: 100%;
    }

    .banners_4 .container .box h5{
        font-size: 14px;
    }
    .banners_4 .container .box h6{
        font-size: 13px;
    }

    .banners .banners_boxs.banner_3_img .box{
        width: 100%;
    }




    .product .icons .btn_add_cart.active{
        font-size: 13px;
    }
    .product .icons .btn_add_cart.active i{
        font-size: 14px;
    }


    footer .container .row{
        width: 100%;
    }
    footer .container .big_row{
        width: 100%;
    }
}















/* End Cart */ 




/* checkout page */ 

.checkout{
    margin: 50px 0;
}

.checkout .container{
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.checkout .ordersummary{
    width: 45%;
    padding: 0 20px;
    border: 2px solid var(--border_color);
    border-radius: 5px;
    box-shadow: 0px 8px 10px #c0bfbf44;
}

.checkout .ordersummary h1{
    border-bottom: 1px solid var(--border_color);
    padding: 20px 0;
    margin-bottom: 20px;
    color: var(--main_color);
}

.checkout .ordersummary .items{
    height: 350px;
    overflow-y: auto;
}

.checkout .ordersummary .item_cart{
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: space-between;
    height: 125px;
    border-bottom: 1px solid var(--border_color);
    padding-right: 20px;
}
.checkout .ordersummary .item_cart:last-child{
    border-bottom: 0;
}

.checkout .ordersummary .item_cart .image_name{
    display: flex;
    align-items: center;
    gap: 20px;
}

.checkout .ordersummary .item_cart img{
    width: 80px;
}
.checkout .ordersummary .item_cart  h4{
    margin-bottom: 10px;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.checkout .ordersummary  .delete_item i{
    font-size: 22px;
    cursor: pointer;
    transition: 0.3s;
}
.checkout .ordersummary  .delete_item i:hover{
    color: #E51A1A;
    scale: 1.1;
}

.checkout .ordersummary .quantity_control{
    display: flex;
    align-items: center;
    gap: 5px;
    margin-top: 5px;
}

.checkout .ordersummary .quantity_control span{
    font-size: 18px;
    min-width: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--bg_color);
    padding: 3px 0;

}

.checkout .ordersummary .quantity_control button{
    width: 27px;
    height: 27px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 20px;
    border-radius: 2px;
    border: 1px solid var(--border_color);
}


.checkout .ordersummary .bottom_summary{
    border-top: 1px solid var(--border_color);
    padding-top: 25PX;
}
.checkout .ordersummary .shop_table{
    margin-bottom: 20PX;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.checkout .ordersummary .shop_table p{
    font-size: 20px;
    color: var(--color_heading);
    text-transform: capitalize;
}

.checkout .ordersummary .shop_table  span{
    font-weight: bold;
    font-size: 20px;
}

.checkout .ordersummary .button_div{
    border-top: 1px solid var(--border_color);
    padding: 30px 0;
}


.checkout .ordersummary .button_div button{
    width: 100%;
    background: var(--main_color);
    color: var(--white_color);
    border: 2px solid var(--main_color);
    padding: 15px 0;
    outline: none;
    border-radius: 3px;
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    transition: 0.3s ease-in-out;
}
.checkout .ordersummary .button_div button:hover{
    background: transparent;
    color: var(--main_color);
}


.checkout .input_info{
    width: 45%;
}

.checkout .input_info h2{
    padding: 10px 20px;
    background: var(--main_color);
    text-transform: capitalize;
    color: var(--white_color);
}

.checkout .input_info .address,
.checkout .input_info .coupon{
    margin-bottom: 25px;
    border-radius: 5px;
    overflow: hidden;
    border: 1px solid var(--border_color);
    box-shadow: 0px 8px 10px #c0bfbf44;
}
.checkout .input_info .coupon .btn_coupon{
    text-align: center;
    padding-bottom: 20px;
}

.checkout .input_info .coupon .btn_coupon button{
    width: 50%;
    background: var(--main_color);
    color: var(--white_color);
    border: 2px solid var(--main_color);
    padding: 12px 0;
    outline: none;
    border-radius: 2px ;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: 0.3s ease-in-out;
}
.checkout .input_info .coupon .btn_coupon button:hover{
    background: transparent;
    color: var(--main_color);
}

.checkout .input_info .inputs{
    display: flex;
    flex-direction: column;
    padding: 15px 20px;
}

.checkout .input_info .inputs label{
    color: var(--color_heading);
    font-size: 14px;
    text-transform: capitalize;
}

.checkout .input_info .inputs input{
    margin: 8px 0 22px;
    padding: 15px 10px;
    outline: none;
    border: 1px solid var(--border_color);
    border-radius: 5px;
    transition: 0.3s;
}
.checkout .input_info .inputs input:focus{
    border-color: var(--main_color);
}