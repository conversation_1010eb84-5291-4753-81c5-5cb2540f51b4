// Enhanced Search Functionality

let allProducts = [];
let searchSuggestions = [];

// Initialize search functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeSearch();
    loadProducts();
});

function initializeSearch() {
    const searchInput = document.getElementById('search');
    const searchForm = document.getElementById('search-form');
    const clearButton = document.getElementById('clear-search');
    const suggestionsContainer = document.getElementById('search-suggestions');
    
    if (!searchInput) return;
    
    // Debounced search function
    const debouncedSearch = debounce(handleSearchInput, 300);
    
    searchInput.addEventListener('input', function(e) {
        const query = e.target.value.trim();
        
        if (query.length > 0) {
            clearButton.classList.add('visible');
            debouncedSearch(query);
        } else {
            clearButton.classList.remove('visible');
            hideSuggestions();
        }
    });
    
    // Clear search
    clearButton.addEventListener('click', function() {
        searchInput.value = '';
        clearButton.classList.remove('visible');
        hideSuggestions();
        searchInput.focus();
    });
    
    // Handle form submission
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const query = searchInput.value.trim();
        if (query) {
            performSearch(query);
            hideSuggestions();
        }
    });
    
    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchForm.contains(e.target)) {
            hideSuggestions();
        }
    });
    
    // Handle keyboard navigation
    searchInput.addEventListener('keydown', function(e) {
        const suggestions = suggestionsContainer.querySelectorAll('.search-suggestion');
        const activeSuggestion = suggestionsContainer.querySelector('.search-suggestion.active');
        
        switch(e.key) {
            case 'ArrowDown':
                e.preventDefault();
                navigateSuggestions(suggestions, activeSuggestion, 'down');
                break;
            case 'ArrowUp':
                e.preventDefault();
                navigateSuggestions(suggestions, activeSuggestion, 'up');
                break;
            case 'Enter':
                if (activeSuggestion) {
                    e.preventDefault();
                    activeSuggestion.click();
                }
                break;
            case 'Escape':
                hideSuggestions();
                break;
        }
    });
}

function loadProducts() {
    fetch('products.json')
        .then(response => response.json())
        .then(data => {
            allProducts = data;
            generateSearchSuggestions();
        })
        .catch(error => {
            console.error('Error loading products:', error);
        });
}

function generateSearchSuggestions() {
    searchSuggestions = [];
    
    allProducts.forEach(product => {
        // Add product names
        searchSuggestions.push({
            type: 'product',
            text: product.name,
            data: product
        });
        
        // Add categories
        if (product.catetory && !searchSuggestions.find(s => s.text === product.catetory && s.type === 'category')) {
            searchSuggestions.push({
                type: 'category',
                text: product.catetory,
                data: product.catetory
            });
        }
        
        // Add brand names (extract from product name)
        const words = product.name.split(' ');
        const potentialBrand = words[0];
        if (potentialBrand.length > 2 && !searchSuggestions.find(s => s.text === potentialBrand && s.type === 'brand')) {
            searchSuggestions.push({
                type: 'brand',
                text: potentialBrand,
                data: potentialBrand
            });
        }
    });
}

function handleSearchInput(query) {
    const suggestions = getSearchSuggestions(query);
    displaySuggestions(suggestions);
}

function getSearchSuggestions(query) {
    const lowerQuery = query.toLowerCase();
    const filtered = searchSuggestions.filter(suggestion => 
        suggestion.text.toLowerCase().includes(lowerQuery)
    );
    
    // Sort by relevance (exact matches first, then starts with, then contains)
    filtered.sort((a, b) => {
        const aText = a.text.toLowerCase();
        const bText = b.text.toLowerCase();
        
        if (aText === lowerQuery) return -1;
        if (bText === lowerQuery) return 1;
        if (aText.startsWith(lowerQuery)) return -1;
        if (bText.startsWith(lowerQuery)) return 1;
        return 0;
    });
    
    return filtered.slice(0, 8); // Limit to 8 suggestions
}

function displaySuggestions(suggestions) {
    const container = document.getElementById('search-suggestions');
    
    if (suggestions.length === 0) {
        hideSuggestions();
        return;
    }
    
    container.innerHTML = suggestions.map(suggestion => {
        const icon = getSuggestionIcon(suggestion.type);
        return `
            <div class="search-suggestion" data-type="${suggestion.type}" data-text="${suggestion.text}">
                <i class="${icon}"></i>
                <span>${highlightMatch(suggestion.text, document.getElementById('search').value)}</span>
                <small class="suggestion-type">${suggestion.type}</small>
            </div>
        `;
    }).join('');
    
    container.style.display = 'block';
    
    // Add click handlers
    container.querySelectorAll('.search-suggestion').forEach(suggestion => {
        suggestion.addEventListener('click', function() {
            const text = this.dataset.text;
            const type = this.dataset.type;
            
            document.getElementById('search').value = text;
            hideSuggestions();
            
            if (type === 'product') {
                performSearch(text);
            } else if (type === 'category') {
                filterByCategory(text);
            } else if (type === 'brand') {
                filterByBrand(text);
            }
        });
    });
}

function getSuggestionIcon(type) {
    const icons = {
        product: 'fas fa-box',
        category: 'fas fa-tags',
        brand: 'fas fa-building'
    };
    return icons[type] || 'fas fa-search';
}

function highlightMatch(text, query) {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<strong>$1</strong>');
}

function hideSuggestions() {
    const container = document.getElementById('search-suggestions');
    container.style.display = 'none';
    container.innerHTML = '';
}

function navigateSuggestions(suggestions, activeSuggestion, direction) {
    if (suggestions.length === 0) return;
    
    // Remove current active class
    if (activeSuggestion) {
        activeSuggestion.classList.remove('active');
    }
    
    let nextIndex = 0;
    
    if (activeSuggestion) {
        const currentIndex = Array.from(suggestions).indexOf(activeSuggestion);
        if (direction === 'down') {
            nextIndex = (currentIndex + 1) % suggestions.length;
        } else {
            nextIndex = currentIndex === 0 ? suggestions.length - 1 : currentIndex - 1;
        }
    }
    
    suggestions[nextIndex].classList.add('active');
}

function performSearch(query) {
    const results = searchProducts(query);
    displaySearchResults(results, query);
    
    // Add to search history
    addToSearchHistory(query);
    
    showNotification(`Found ${results.length} results for "${query}"`);
}

function searchProducts(query) {
    const lowerQuery = query.toLowerCase();
    
    return allProducts.filter(product => {
        return product.name.toLowerCase().includes(lowerQuery) ||
               product.catetory.toLowerCase().includes(lowerQuery);
    });
}

function displaySearchResults(results, query) {
    // Clear existing product displays
    const productContainers = [
        'swiper_items_sale',
        'swiper_elctronics',
        'swiper_appliances',
        'swiper_mobiles'
    ];
    
    productContainers.forEach(containerId => {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = '';
        }
    });
    
    // Display results in the first container
    const mainContainer = document.getElementById('swiper_items_sale');
    if (mainContainer && results.length > 0) {
        // Update section title
        const titleElement = mainContainer.closest('.slide').querySelector('.top_slide h2');
        if (titleElement) {
            titleElement.innerHTML = `<i class="fas fa-search"></i> Search Results for "${query}"`;
        }
        
        displayProductsInContainer(results, mainContainer);
    }
    
    // Hide other sections if search results are shown
    if (results.length > 0) {
        hideOtherSections();
    }
}

function displayProductsInContainer(products, container) {
    container.innerHTML = products.map(product => {
        const salePrice = product.old_price ? 
            `<span class="old_price">$${product.old_price}</span>` : '';
        
        return `
            <div class="swiper-slide">
                <div class="product" data-aos="fade-up">
                    ${product.old_price ? `<span class="sale_present">Sale</span>` : ''}
                    <div class="img_product">
                        <img src="${product.img}" alt="${product.name}">
                    </div>
                    <h3 class="name_product">
                        <a href="#">${product.name}</a>
                    </h3>
                    <div class="stars">
                        <i class="fa-solid fa-star"></i>
                        <i class="fa-solid fa-star"></i>
                        <i class="fa-solid fa-star"></i>
                        <i class="fa-solid fa-star"></i>
                        <i class="fa-regular fa-star"></i>
                    </div>
                    <div class="price">
                        <p><span>$${product.price}</span> ${salePrice}</p>
                    </div>
                    <div class="product-actions">
                        <button class="btn_add_cart btn" data-id="${product.id}">
                            <i class="fa-solid fa-cart-shopping"></i> Add to Cart
                        </button>
                        <button class="btn-wishlist" onclick="addToWishlist(${product.id})" title="Add to Wishlist">
                            <i class="fa-regular fa-heart"></i>
                        </button>
                        <button class="btn-compare" onclick="addToCompare(${product.id})" title="Compare">
                            <i class="fas fa-balance-scale"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

function hideOtherSections() {
    const sections = document.querySelectorAll('.slide');
    sections.forEach((section, index) => {
        if (index > 0) { // Keep first section for search results
            section.style.display = 'none';
        }
    });
}

function filterByCategory(category) {
    const results = allProducts.filter(product => 
        product.catetory.toLowerCase() === category.toLowerCase()
    );
    displaySearchResults(results, `Category: ${category}`);
}

function filterByBrand(brand) {
    const results = allProducts.filter(product => 
        product.name.toLowerCase().startsWith(brand.toLowerCase())
    );
    displaySearchResults(results, `Brand: ${brand}`);
}

function addToSearchHistory(query) {
    let history = JSON.parse(localStorage.getItem('searchHistory')) || [];
    
    // Remove if already exists
    history = history.filter(item => item !== query);
    
    // Add to beginning
    history.unshift(query);
    
    // Keep only last 10 searches
    history = history.slice(0, 10);
    
    localStorage.setItem('searchHistory', JSON.stringify(history));
}

// Utility function (if not already defined)
if (typeof debounce === 'undefined') {
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}
