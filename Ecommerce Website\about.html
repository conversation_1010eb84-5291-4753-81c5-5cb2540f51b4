<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us | TechMart</title>
    <meta name="description" content="Learn about TechMart - your trusted partner for premium electronics and technology products.">

    <link rel="icon" href="img/icon.png">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- File CSS -->
    <link rel="stylesheet" href="CSS/style.css">
    <link rel="stylesheet" href="CSS/enhancements.css">
</head>
<body>
    <!-- Theme Toggle -->
    <div class="theme-toggle" id="theme-toggle">
        <i class="fas fa-moon"></i>
    </div>

    <!-- Back to Top Button -->
    <button id="back-to-top" class="back-to-top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Header -->
    <header>
        <div class="top_header">
            <div class="container">
                <a href="index.html" class="logo"> <img src="img/logo.png" alt="TechMart"></a>

                <form action="" class="search_box" id="search-form">
                    <div class="select_box">
                        <select id="category" name="category">
                            <option value="All Categories">All Categories</option>
                            <option value="electronics">Electronics & Digital</option>
                            <option value="mobiles">Phones & Tablets</option>
                            <option value="appliances">Home Appliances</option>
                        </select>
                    </div>

                    <div class="search-input-container">
                        <input type="text" name="search" id="search" placeholder="Search for products..." autocomplete="off">
                        <div id="search-suggestions" class="search-suggestions"></div>
                    </div>

                    <button type="submit" class="search-btn">
                        <i class="fa-solid fa-magnifying-glass"></i>
                    </button>
                </form>

                <div class="header_icons">
                    <div class="icon" id="wishlist-icon" title="Wishlist">
                        <i class="fa-regular fa-heart"></i>
                        <span class="count count_favourite">0</span>
                    </div>

                    <div class="icon cart-icon" title="Shopping Cart">
                        <i class="fa-solid fa-cart-shopping"></i>
                        <span class="count count_item_header">0</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="bottom_header">
            <div class="container">
                <nav class="nav">
                    <div class="category_nav">
                        <div onclick="Open_Categ_list()" class="category_btn">
                            <i class="fa-solid fa-bars"></i>
                            <p>Browse Category</p>
                            <i class="fa-solid fa-angle-down"></i>
                        </div>

                        <div class="category_nav_list">
                            <a href="#">Electronics & Digital</a>
                            <a href="#">Phones & Tablets</a>
                            <a href="#">Home Appliances</a>
                        </div>
                    </div>

                    <ul class="nav_links">
                        <li><a href="index.html">Home</a></li>
                        <li class="active"><a href="about.html">About</a></li>
                        <li><a href="#">Contact</a></li>
                    </ul>
                </nav>

                <div class="login_signup btns">
                    <a href="#" class="btn">Login <i class="fa-solid fa-right-to-bracket"></i></a>
                    <a href="#" class="btn">Sign UP <i class="fa-solid fa-user-plus"></i></a>
                </div>
            </div>
        </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb" aria-label="Breadcrumb">
        <div class="container">
            <ol class="breadcrumb-list">
                <li class="breadcrumb-item">
                    <a href="index.html"><i class="fas fa-home"></i> Home</a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">About Us</li>
            </ol>
        </div>
    </nav>

    <!-- About Section -->
    <section class="about-hero" data-aos="fade-up">
        <div class="container">
            <div class="about-hero-content">
                <h1>About TechMart</h1>
                <p class="hero-subtitle">Your trusted partner for premium electronics and technology products</p>
            </div>
        </div>
    </section>

    <!-- Company Story -->
    <section class="company-story" data-aos="fade-up">
        <div class="container">
            <div class="story-content">
                <div class="story-text">
                    <h2>Our Story</h2>
                    <p>Founded in 2020, TechMart has grown from a small startup to a leading online electronics retailer. We're passionate about bringing the latest technology to our customers at competitive prices.</p>
                    <p>Our mission is to make cutting-edge technology accessible to everyone, while providing exceptional customer service and support.</p>
                </div>
                <div class="story-stats">
                    <div class="stat-item">
                        <h3>50K+</h3>
                        <p>Happy Customers</p>
                    </div>
                    <div class="stat-item">
                        <h3>1000+</h3>
                        <p>Products</p>
                    </div>
                    <div class="stat-item">
                        <h3>24/7</h3>
                        <p>Support</p>
                    </div>
                    <div class="stat-item">
                        <h3>99%</h3>
                        <p>Satisfaction</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Values Section -->
    <section class="values-section" data-aos="fade-up">
        <div class="container">
            <h2>Our Values</h2>
            <div class="values-grid">
                <div class="value-item">
                    <div class="value-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Quality Assurance</h3>
                    <p>We ensure all products meet the highest quality standards before reaching our customers.</p>
                </div>
                <div class="value-item">
                    <div class="value-icon">
                        <i class="fas fa-shipping-fast"></i>
                    </div>
                    <h3>Fast Delivery</h3>
                    <p>Quick and reliable shipping to get your products to you as soon as possible.</p>
                </div>
                <div class="value-item">
                    <div class="value-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3>Customer Support</h3>
                    <p>24/7 customer support to help you with any questions or concerns.</p>
                </div>
                <div class="value-item">
                    <div class="value-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <h3>Best Prices</h3>
                    <p>Competitive pricing and regular deals to give you the best value for money.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="big_row">
                <img class="logo_footer" src="img/white_logo.png" alt="">
                <p>Your trusted partner for premium electronics and technology products.</p>
                <div class="icons_footer">
                    <a href="#"><i class="fa-solid fa-phone"></i></a>
                    <a href="#"><i class="fa-brands fa-facebook-f"></i></a>
                    <a href="#"><i class="fa-brands fa-instagram"></i></a>
                    <a href="#"><i class="fa-brands fa-x-twitter"></i></a>
                </div>
            </div>

            <div class="row">
                <h4>Quick Links</h4>
                <div class="links">
                    <a href="#"><i class="fa-solid fa-caret-right"></i> Your Account</a>
                    <a href="#"><i class="fa-solid fa-caret-right"></i> Returns & Exchanges</a>
                    <a href="#"><i class="fa-solid fa-caret-right"></i> Shipping & Delivery</a>
                    <a href="#"><i class="fa-solid fa-caret-right"></i> Help & Support</a>
                </div>
            </div>
        </div>

        <div class="bottom_footer">
            <div class="container">
                <div class="footer-bottom-content">
                    <p>&copy; 2024 <span>TechMart.</span> All Rights Reserved.</p>
                    <div class="footer-links">
                        <a href="#">Privacy Policy</a>
                        <a href="#">Terms of Service</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="JS/enhancements.js"></script>
    
    <script>
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });
    </script>

    <style>
        .about-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }

        .about-hero h1 {
            font-size: 48px;
            margin-bottom: 20px;
            color: white;
        }

        .hero-subtitle {
            font-size: 20px;
            opacity: 0.9;
        }

        .company-story {
            padding: 80px 0;
        }

        .story-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .story-text h2 {
            margin-bottom: 24px;
            font-size: 36px;
        }

        .story-text p {
            margin-bottom: 20px;
            line-height: 1.6;
            font-size: 16px;
        }

        .story-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            background: var(--bg_color);
            border-radius: 12px;
        }

        .stat-item h3 {
            font-size: 32px;
            color: var(--main_color);
            margin-bottom: 8px;
        }

        .values-section {
            padding: 80px 0;
            background: var(--bg_color);
        }

        .values-section h2 {
            text-align: center;
            margin-bottom: 60px;
            font-size: 36px;
        }

        .values-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
        }

        .value-item {
            text-align: center;
            padding: 40px 20px;
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow-light);
            transition: var(--transition);
        }

        .value-item:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .value-icon {
            width: 80px;
            height: 80px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
        }

        .value-icon i {
            font-size: 32px;
            color: white;
        }

        .value-item h3 {
            margin-bottom: 16px;
            font-size: 20px;
        }

        @media (max-width: 768px) {
            .about-hero h1 {
                font-size: 32px;
            }

            .story-content {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .story-stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }

            .values-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
        }
    </style>
</body>
</html>
