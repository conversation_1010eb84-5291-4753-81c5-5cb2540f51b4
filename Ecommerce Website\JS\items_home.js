fetch('products.json')
.then(response => response.json())
.then(data => {
 
    const cart = JSON.parse(localStorage.getItem('cart')) || []

    const swiper_items_sale = document.getElementById("swiper_items_sale")

    const swiper_elctronics = document.getElementById("swiper_elctronics")


    const swiper_appliances = document.getElementById("swiper_appliances")

    const swiper_mobiles = document.getElementById("swiper_mobiles")


    data.forEach(product => {
        if(product.old_price){

            const isInCart = cart.some(cartItem => cartItem.id === product.id)

            const percent_disc = Math.floor((product.old_price - product.price) / product.old_price * 100)
            
            swiper_items_sale.innerHTML += `


             <div class="swiper-slide product">
                        <span class="sale_present">%${percent_disc}</span>

                        <div class="img_product">
                            <a href="product-detail.html?id=${product.id}"><img src="${product.img}" alt="${product.name}"></a>
                        </div>

                        <div class="stars">
                            ${generateStarRating(product.rating || 4)}
                        </div>

                        <h3 class="name_product">
                            <a href="product-detail.html?id=${product.id}">${product.name}</a>
                        </h3>

                        <div class="price">
                            <p><span>$${product.price}</span></p>
                            <p class="old_price">$${product.old_price}</p>
                        </div>

                        <div class="product-actions">
                            <button class="btn_add_cart btn ${isInCart ? 'active' : ''}" data-id="${product.id}">
                                <i class="fa-solid fa-cart-shopping"></i> ${isInCart ? 'Item in cart' : 'Add to cart'}
                            </button>
                            <button class="btn-wishlist" onclick="addToWishlist(${product.id})" title="Add to Wishlist">
                                <i class="fa-regular fa-heart"></i>
                            </button>
                            <button class="btn-compare" onclick="addToCompare(${product.id})" title="Compare">
                                <i class="fas fa-balance-scale"></i>
                            </button>
                        </div>
                    </div>
            
            
            
            
            `
            
            
        }
    })


    data.forEach(product => {
        if(product.catetory == "electronics"){


            const isInCart = cart.some(cartItem => cartItem.id === product.id)


            const old_price_Pargrahp = product.old_price ? `<p class="old_price">$${product.old_price}</p>` : "";

            const percent_disc_div = product.old_price ? `<span class="sale_present">%${Math.floor((product.old_price - product.price) / product.old_price * 100)}</span>` : "";



            swiper_elctronics.innerHTML += `


            <div class="swiper-slide product">
                       
                        ${percent_disc_div}
                       <div class="img_product">
                           <a href="product-detail.html?id=${product.id}"><img src="${product.img}" alt="${product.name}"></a>
                       </div>

                       <div class="stars">
                           ${generateStarRating(product.rating || 4)}
                       </div>

                       <h3 class="name_product">
                           <a href="product-detail.html?id=${product.id}">${product.name}</a>
                       </h3>

                       <div class="price">
                           <p><span>$${product.price}</span></p>
                           ${old_price_Pargrahp}
                       </div>

                       <div class="product-actions">
                           <button class="btn_add_cart btn ${isInCart ? 'active' : ''}" data-id="${product.id}">
                                <i class="fa-solid fa-cart-shopping"></i> ${isInCart ? 'Item in cart' : 'Add to cart'}
                            </button>
                           <button class="btn-wishlist" onclick="addToWishlist(${product.id})" title="Add to Wishlist">
                               <i class="fa-regular fa-heart"></i>
                           </button>
                           <button class="btn-compare" onclick="addToCompare(${product.id})" title="Compare">
                               <i class="fas fa-balance-scale"></i>
                           </button>
                       </div>
                   </div>
           
           
           
           
           `



        }
    })


    data.forEach(product => {
        if(product.catetory == "appliances"){

            const isInCart = cart.some(cartItem => cartItem.id === product.id)

            const old_price_Pargrahp = product.old_price ? `<p class="old_price">$${product.old_price}</p>` : "";

            const percent_disc_div = product.old_price ? `<span class="sale_present">%${Math.floor((product.old_price - product.price) / product.old_price * 100)}</span>` : "";



            swiper_appliances.innerHTML += `


            <div class="swiper-slide product">
                       
                        ${percent_disc_div}
                       <div class="img_product">
                           <a href="product-detail.html?id=${product.id}"><img src="${product.img}" alt="${product.name}"></a>
                       </div>

                       <div class="stars">
                           ${generateStarRating(product.rating || 4)}
                       </div>

                       <h3 class="name_product">
                           <a href="product-detail.html?id=${product.id}">${product.name}</a>
                       </h3>

                       <div class="price">
                           <p><span>$${product.price}</span></p>
                           ${old_price_Pargrahp}
                       </div>

                       <div class="product-actions">
                        <button class="btn_add_cart btn ${isInCart ? 'active' : ''}" data-id="${product.id}">
                                <i class="fa-solid fa-cart-shopping"></i> ${isInCart ? 'Item in cart' : 'Add to cart'}
                            </button>
                           <button class="btn-wishlist" onclick="addToWishlist(${product.id})" title="Add to Wishlist">
                               <i class="fa-regular fa-heart"></i>
                           </button>
                           <button class="btn-compare" onclick="addToCompare(${product.id})" title="Compare">
                               <i class="fas fa-balance-scale"></i>
                           </button>
                       </div>
                   </div>
           
           
           
           
           `



        }
    })


    data.forEach(product => {
        if(product.catetory == "mobiles"){

            const isInCart = cart.some(cartItem => cartItem.id === product.id)

            const old_price_Pargrahp = product.old_price ? `<p class="old_price">$${product.old_price}</p>` : "";

            const percent_disc_div = product.old_price ? `<span class="sale_present">%${Math.floor((product.old_price - product.price) / product.old_price * 100)}</span>` : "";



            swiper_mobiles.innerHTML += `


            <div class="swiper-slide product">
                       
                        ${percent_disc_div}
                       <div class="img_product">
                           <a href="product-detail.html?id=${product.id}"><img src="${product.img}" alt="${product.name}"></a>
                       </div>

                       <div class="stars">
                           ${generateStarRating(product.rating || 4)}
                       </div>

                       <h3 class="name_product">
                           <a href="product-detail.html?id=${product.id}">${product.name}</a>
                       </h3>

                       <div class="price">
                           <p><span>$${product.price}</span></p>
                           ${old_price_Pargrahp}
                       </div>

                       <div class="product-actions">
                         <button class="btn_add_cart btn ${isInCart ? 'active' : ''}" data-id="${product.id}">
                                <i class="fa-solid fa-cart-shopping"></i> ${isInCart ? 'Item in cart' : 'Add to cart'}
                            </button>
                           <button class="btn-wishlist" onclick="addToWishlist(${product.id})" title="Add to Wishlist">
                               <i class="fa-regular fa-heart"></i>
                           </button>
                           <button class="btn-compare" onclick="addToCompare(${product.id})" title="Compare">
                               <i class="fas fa-balance-scale"></i>
                           </button>
                       </div>
                   </div>
           
           
           
           
           `



        }
    })

    // Store products globally for other scripts
    window.allProducts = data;
})

// Helper function to generate star ratings
function generateStarRating(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    let stars = '';
    for (let i = 0; i < fullStars; i++) {
        stars += '<i class="fas fa-star"></i>';
    }
    if (hasHalfStar) {
        stars += '<i class="fas fa-star-half-alt"></i>';
    }
    for (let i = 0; i < emptyStars; i++) {
        stars += '<i class="far fa-star"></i>';
    }

    return stars;
}