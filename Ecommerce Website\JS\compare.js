// Product Compare Functionality

let compareProducts = [];
const MAX_COMPARE_ITEMS = 4;

// Initialize compare functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeCompare();
    loadCompareList();
});

function initializeCompare() {
    const compareIcon = document.getElementById('compare-icon');
    const compareModal = document.getElementById('compare-modal');
    
    if (compareIcon) {
        compareIcon.addEventListener('click', function() {
            openCompareModal();
        });
    }
    
    // Close modal functionality
    if (compareModal) {
        const closeBtn = compareModal.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', closeCompareModal);
        }
        
        compareModal.addEventListener('click', function(e) {
            if (e.target === compareModal) {
                closeCompareModal();
            }
        });
    }
    
    // Add compare buttons to existing products
    addCompareButtonsToProducts();
}

function loadCompareList() {
    const compareIds = JSON.parse(localStorage.getItem('compareList')) || [];
    
    if (typeof allProducts !== 'undefined' && allProducts.length > 0) {
        updateCompareProducts(compareIds);
    } else {
        // Load products if not already loaded
        fetch('products.json')
            .then(response => response.json())
            .then(data => {
                if (typeof allProducts === 'undefined') {
                    window.allProducts = data;
                }
                updateCompareProducts(compareIds);
            })
            .catch(error => {
                console.error('Error loading products for comparison:', error);
            });
    }
}

function updateCompareProducts(compareIds) {
    compareProducts = allProducts.filter(product => 
        compareIds.includes(product.id)
    );
    updateCompareCount();
    updateCompareButtons();
}

function addToCompare(productId) {
    let compareIds = JSON.parse(localStorage.getItem('compareList')) || [];
    
    if (compareIds.includes(productId)) {
        removeFromCompare(productId);
        return;
    }
    
    if (compareIds.length >= MAX_COMPARE_ITEMS) {
        showNotification(`You can only compare up to ${MAX_COMPARE_ITEMS} products`, 'warning');
        return;
    }
    
    compareIds.push(productId);
    localStorage.setItem('compareList', JSON.stringify(compareIds));
    
    const product = allProducts.find(p => p.id === productId);
    if (product) {
        compareProducts.push(product);
    }
    
    updateCompareCount();
    updateCompareButtons();
    showNotification('Added to comparison!', 'success');
    
    // Update button state
    const buttons = document.querySelectorAll(`[data-product-id="${productId}"] .btn-compare`);
    buttons.forEach(btn => {
        btn.classList.add('active');
        btn.innerHTML = '<i class="fas fa-check"></i>';
        btn.title = 'Remove from Compare';
    });
}

function removeFromCompare(productId) {
    let compareIds = JSON.parse(localStorage.getItem('compareList')) || [];
    compareIds = compareIds.filter(id => id !== productId);
    localStorage.setItem('compareList', JSON.stringify(compareIds));
    
    compareProducts = compareProducts.filter(product => product.id !== productId);
    
    updateCompareCount();
    updateCompareButtons();
    updateCompareModal();
    showNotification('Removed from comparison!', 'info');
    
    // Update button state
    const buttons = document.querySelectorAll(`[data-product-id="${productId}"] .btn-compare`);
    buttons.forEach(btn => {
        btn.classList.remove('active');
        btn.innerHTML = '<i class="fas fa-balance-scale"></i>';
        btn.title = 'Add to Compare';
    });
}

function updateCompareCount() {
    const countElements = document.querySelectorAll('.count_compare');
    countElements.forEach(el => {
        el.textContent = compareProducts.length;
    });
}

function updateCompareButtons() {
    const compareIds = compareProducts.map(p => p.id);
    
    document.querySelectorAll('.btn-compare').forEach(btn => {
        const productCard = btn.closest('[data-product-id]');
        if (productCard) {
            const productId = parseInt(productCard.dataset.productId);
            
            if (compareIds.includes(productId)) {
                btn.classList.add('active');
                btn.innerHTML = '<i class="fas fa-check"></i>';
                btn.title = 'Remove from Compare';
            } else {
                btn.classList.remove('active');
                btn.innerHTML = '<i class="fas fa-balance-scale"></i>';
                btn.title = 'Add to Compare';
            }
        }
    });
}

function addCompareButtonsToProducts() {
    const products = document.querySelectorAll('.product');
    
    products.forEach(product => {
        if (!product.querySelector('.btn-compare')) {
            const productId = getProductIdFromElement(product);
            if (productId) {
                product.dataset.productId = productId;
                
                const actionsContainer = product.querySelector('.product-actions') || 
                                       createProductActionsContainer(product);
                
                const compareBtn = document.createElement('button');
                compareBtn.className = 'btn-compare';
                compareBtn.innerHTML = '<i class="fas fa-balance-scale"></i>';
                compareBtn.title = 'Add to Compare';
                compareBtn.onclick = () => addToCompare(productId);
                
                actionsContainer.appendChild(compareBtn);
            }
        }
    });
}

function getProductIdFromElement(productElement) {
    // Try to get product ID from add to cart button
    const addToCartBtn = productElement.querySelector('.btn_add_cart[data-id]');
    if (addToCartBtn) {
        return parseInt(addToCartBtn.dataset.id);
    }
    
    // Try to get from product element itself
    if (productElement.dataset.productId) {
        return parseInt(productElement.dataset.productId);
    }
    
    return null;
}

function createProductActionsContainer(product) {
    const actionsContainer = document.createElement('div');
    actionsContainer.className = 'product-actions';
    
    const addToCartBtn = product.querySelector('.btn_add_cart');
    if (addToCartBtn) {
        addToCartBtn.parentNode.insertBefore(actionsContainer, addToCartBtn.nextSibling);
        actionsContainer.appendChild(addToCartBtn);
    } else {
        product.appendChild(actionsContainer);
    }
    
    return actionsContainer;
}

function openCompareModal() {
    const modal = document.getElementById('compare-modal');
    if (modal) {
        modal.style.display = 'flex';
        updateCompareModal();
        
        // Add animation
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }
}

function closeCompareModal() {
    const modal = document.getElementById('compare-modal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }
}

function updateCompareModal() {
    const compareContainer = document.getElementById('compare-items');
    if (!compareContainer) return;
    
    if (compareProducts.length === 0) {
        compareContainer.innerHTML = `
            <div class="empty-compare">
                <i class="fas fa-balance-scale" style="font-size: 48px; color: var(--p_color); margin-bottom: 20px;"></i>
                <h3>No products to compare</h3>
                <p>Add some products to compare their features</p>
                <button class="btn" onclick="closeCompareModal()">Continue Shopping</button>
            </div>
        `;
        return;
    }
    
    if (compareProducts.length === 1) {
        compareContainer.innerHTML = `
            <div class="single-compare">
                <h3>Add more products to compare</h3>
                <p>You need at least 2 products to make a comparison</p>
                ${renderSingleProduct(compareProducts[0])}
                <button class="btn" onclick="closeCompareModal()">Add More Products</button>
            </div>
        `;
        return;
    }
    
    compareContainer.innerHTML = renderComparisonTable();
}

function renderSingleProduct(product) {
    return `
        <div class="single-product-preview">
            <img src="${product.img}" alt="${product.name}">
            <h4>${product.name}</h4>
            <p class="price">$${product.price}</p>
            <button class="btn-remove" onclick="removeFromCompare(${product.id})">
                <i class="fas fa-times"></i> Remove
            </button>
        </div>
    `;
}

function renderComparisonTable() {
    const features = extractFeatures(compareProducts);
    
    return `
        <div class="comparison-table">
            <div class="comparison-header">
                <div class="feature-column">
                    <h4>Features</h4>
                </div>
                ${compareProducts.map(product => `
                    <div class="product-column">
                        <div class="product-header">
                            <img src="${product.img}" alt="${product.name}">
                            <h5>${product.name}</h5>
                            <button class="btn-remove" onclick="removeFromCompare(${product.id})">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
            
            <div class="comparison-body">
                ${features.map(feature => `
                    <div class="comparison-row">
                        <div class="feature-cell">
                            <strong>${feature.name}</strong>
                        </div>
                        ${compareProducts.map(product => `
                            <div class="product-cell">
                                ${getProductFeatureValue(product, feature.key)}
                            </div>
                        `).join('')}
                    </div>
                `).join('')}
            </div>
            
            <div class="comparison-actions">
                <div class="feature-cell"></div>
                ${compareProducts.map(product => `
                    <div class="product-cell">
                        <button class="btn btn-add-to-cart" onclick="addToCartFromCompare(${product.id})">
                            <i class="fas fa-cart-plus"></i> Add to Cart
                        </button>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

function extractFeatures(products) {
    return [
        { name: 'Price', key: 'price' },
        { name: 'Category', key: 'catetory' },
        { name: 'Original Price', key: 'old_price' },
        { name: 'Savings', key: 'savings' },
        { name: 'Rating', key: 'rating' }
    ];
}

function getProductFeatureValue(product, featureKey) {
    switch(featureKey) {
        case 'price':
            return `<span class="price-value">$${product.price}</span>`;
        case 'catetory':
            return product.catetory || 'N/A';
        case 'old_price':
            return product.old_price ? `$${product.old_price}` : 'N/A';
        case 'savings':
            if (product.old_price) {
                const savings = product.old_price - product.price;
                const percentage = Math.round((savings / product.old_price) * 100);
                return `<span class="savings">$${savings} (${percentage}%)</span>`;
            }
            return 'N/A';
        case 'rating':
            return generateStarRating(4); // Default rating for demo
        default:
            return 'N/A';
    }
}

function generateStarRating(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    
    let stars = '';
    for (let i = 0; i < fullStars; i++) {
        stars += '<i class="fas fa-star"></i>';
    }
    if (hasHalfStar) {
        stars += '<i class="fas fa-star-half-alt"></i>';
    }
    for (let i = 0; i < emptyStars; i++) {
        stars += '<i class="far fa-star"></i>';
    }
    
    return `<div class="star-rating">${stars}</div>`;
}

function addToCartFromCompare(productId) {
    const product = allProducts.find(p => p.id === productId);
    if (product && typeof addToCart === 'function') {
        addToCart(product);
        showNotification('Added to cart!', 'success');
        
        // Update add to cart button state
        const buttons = document.querySelectorAll(`.btn_add_cart[data-id="${productId}"]`);
        buttons.forEach(btn => {
            btn.classList.add('active');
            btn.innerHTML = '<i class="fa-solid fa-cart-shopping"></i> Item in cart';
        });
    }
}

function clearCompareList() {
    localStorage.removeItem('compareList');
    compareProducts = [];
    updateCompareCount();
    updateCompareButtons();
    updateCompareModal();
    showNotification('Comparison list cleared!', 'info');
}

// Export functions for global use
window.addToCompare = addToCompare;
window.removeFromCompare = removeFromCompare;
window.addToCartFromCompare = addToCartFromCompare;
window.clearCompareList = clearCompareList;
