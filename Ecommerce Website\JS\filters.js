// Product Filter Functionality

let filteredProducts = [];
let currentFilters = {
    categories: ['all'],
    priceRange: { min: 0, max: 1000 },
    sortBy: 'default'
};

// Initialize filters
document.addEventListener('DOMContentLoaded', function() {
    initializeFilters();
    loadProductsForFiltering();
});

function initializeFilters() {
    const filterToggle = document.getElementById('filter-toggle');
    const filterContent = document.getElementById('filter-content');
    const priceMinSlider = document.getElementById('price-min');
    const priceMaxSlider = document.getElementById('price-max');
    const priceMinValue = document.getElementById('price-min-value');
    const priceMaxValue = document.getElementById('price-max-value');
    const sortSelect = document.getElementById('sort-products');
    const applyFiltersBtn = document.querySelector('.apply-filters');
    const clearFiltersBtn = document.querySelector('.clear-filters');
    
    if (!filterToggle) return;
    
    // Toggle filter visibility
    filterToggle.addEventListener('click', function() {
        filterContent.classList.toggle('active');
        const icon = this.querySelector('i');
        icon.classList.toggle('fa-sliders-h');
        icon.classList.toggle('fa-times');
    });
    
    // Price range sliders
    if (priceMinSlider && priceMaxSlider) {
        priceMinSlider.addEventListener('input', function() {
            const minVal = parseInt(this.value);
            const maxVal = parseInt(priceMaxSlider.value);
            
            if (minVal >= maxVal) {
                this.value = maxVal - 10;
            }
            
            priceMinValue.textContent = this.value;
            currentFilters.priceRange.min = parseInt(this.value);
            updateSliderTrack();
        });
        
        priceMaxSlider.addEventListener('input', function() {
            const minVal = parseInt(priceMinSlider.value);
            const maxVal = parseInt(this.value);
            
            if (maxVal <= minVal) {
                this.value = minVal + 10;
            }
            
            priceMaxValue.textContent = this.value;
            currentFilters.priceRange.max = parseInt(this.value);
            updateSliderTrack();
        });
    }
    
    // Category checkboxes
    const categoryCheckboxes = document.querySelectorAll('.filter-option input[type="checkbox"]');
    categoryCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (this.value === 'all') {
                if (this.checked) {
                    // Uncheck all other categories
                    categoryCheckboxes.forEach(cb => {
                        if (cb.value !== 'all') cb.checked = false;
                    });
                    currentFilters.categories = ['all'];
                } else {
                    this.checked = true; // Always keep 'all' checked if trying to uncheck
                }
            } else {
                // Uncheck 'all' when selecting specific categories
                const allCheckbox = document.querySelector('input[value="all"]');
                if (allCheckbox) allCheckbox.checked = false;
                
                updateCategoryFilters();
            }
        });
    });
    
    // Sort dropdown
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            currentFilters.sortBy = this.value;
        });
    }
    
    // Apply filters button
    if (applyFiltersBtn) {
        applyFiltersBtn.addEventListener('click', function() {
            applyFilters();
            showNotification('Filters applied successfully!');
        });
    }
    
    // Clear filters button
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', function() {
            clearAllFilters();
            showNotification('Filters cleared!');
        });
    }
}

function loadProductsForFiltering() {
    if (typeof allProducts !== 'undefined' && allProducts.length > 0) {
        filteredProducts = [...allProducts];
        return;
    }
    
    fetch('products.json')
        .then(response => response.json())
        .then(data => {
            filteredProducts = data;
            if (typeof allProducts === 'undefined') {
                window.allProducts = data;
            }
        })
        .catch(error => {
            console.error('Error loading products for filtering:', error);
        });
}

function updateCategoryFilters() {
    const checkedCategories = [];
    const categoryCheckboxes = document.querySelectorAll('.filter-option input[type="checkbox"]:checked');
    
    categoryCheckboxes.forEach(checkbox => {
        if (checkbox.value !== 'all') {
            checkedCategories.push(checkbox.value);
        }
    });
    
    currentFilters.categories = checkedCategories.length > 0 ? checkedCategories : ['all'];
}

function updateSliderTrack() {
    const priceMinSlider = document.getElementById('price-min');
    const priceMaxSlider = document.getElementById('price-max');
    
    if (!priceMinSlider || !priceMaxSlider) return;
    
    const min = parseInt(priceMinSlider.min);
    const max = parseInt(priceMinSlider.max);
    const minVal = parseInt(priceMinSlider.value);
    const maxVal = parseInt(priceMaxSlider.value);
    
    const minPercent = ((minVal - min) / (max - min)) * 100;
    const maxPercent = ((maxVal - min) / (max - min)) * 100;
    
    // Update visual track (if you want to add custom styling)
    priceMinSlider.style.background = `linear-gradient(to right, #ddd ${minPercent}%, var(--main_color) ${minPercent}%, var(--main_color) ${maxPercent}%, #ddd ${maxPercent}%)`;
}

function applyFilters() {
    let filtered = [...allProducts];
    
    // Apply category filter
    if (!currentFilters.categories.includes('all')) {
        filtered = filtered.filter(product => 
            currentFilters.categories.includes(product.catetory)
        );
    }
    
    // Apply price range filter
    filtered = filtered.filter(product => 
        product.price >= currentFilters.priceRange.min && 
        product.price <= currentFilters.priceRange.max
    );
    
    // Apply sorting
    filtered = sortProducts(filtered, currentFilters.sortBy);
    
    filteredProducts = filtered;
    displayFilteredProducts();
    updateFilterStats();
}

function sortProducts(products, sortBy) {
    const sorted = [...products];
    
    switch(sortBy) {
        case 'price-low':
            return sorted.sort((a, b) => a.price - b.price);
        case 'price-high':
            return sorted.sort((a, b) => b.price - a.price);
        case 'name':
            return sorted.sort((a, b) => a.name.localeCompare(b.name));
        case 'newest':
            return sorted.sort((a, b) => b.id - a.id);
        default:
            return sorted;
    }
}

function displayFilteredProducts() {
    // Clear all product containers
    const productContainers = [
        'swiper_items_sale',
        'swiper_elctronics',
        'swiper_appliances',
        'swiper_mobiles'
    ];
    
    productContainers.forEach(containerId => {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = '';
        }
    });
    
    if (filteredProducts.length === 0) {
        displayNoResults();
        return;
    }
    
    // Group products by category for display
    const groupedProducts = groupProductsByCategory(filteredProducts);
    
    // Display each category
    Object.keys(groupedProducts).forEach((category, index) => {
        const containerId = productContainers[index];
        const container = document.getElementById(containerId);
        
        if (container && groupedProducts[category].length > 0) {
            // Update section title
            const section = container.closest('.slide');
            const titleElement = section.querySelector('.top_slide h2');
            if (titleElement) {
                const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
                titleElement.innerHTML = `<i class="fas fa-filter"></i> ${categoryName} (${groupedProducts[category].length})`;
            }
            
            // Show section
            section.style.display = 'block';
            
            // Display products
            displayProductsInContainer(groupedProducts[category], container);
        }
    });
    
    // Hide empty sections
    hideEmptySections(groupedProducts);
}

function groupProductsByCategory(products) {
    const grouped = {};
    
    products.forEach(product => {
        const category = product.catetory || 'other';
        if (!grouped[category]) {
            grouped[category] = [];
        }
        grouped[category].push(product);
    });
    
    return grouped;
}

function displayNoResults() {
    const mainContainer = document.getElementById('swiper_items_sale');
    if (mainContainer) {
        const section = mainContainer.closest('.slide');
        const titleElement = section.querySelector('.top_slide h2');
        
        if (titleElement) {
            titleElement.innerHTML = '<i class="fas fa-search"></i> No Results Found';
        }
        
        mainContainer.innerHTML = `
            <div class="no-results">
                <div class="no-results-content">
                    <i class="fas fa-search" style="font-size: 48px; color: var(--p_color); margin-bottom: 20px;"></i>
                    <h3>No products found</h3>
                    <p>Try adjusting your filters or search terms</p>
                    <button class="btn clear-filters" onclick="clearAllFilters()">Clear All Filters</button>
                </div>
            </div>
        `;
        
        section.style.display = 'block';
    }
    
    // Hide other sections
    const allSections = document.querySelectorAll('.slide');
    allSections.forEach((section, index) => {
        if (index > 0) {
            section.style.display = 'none';
        }
    });
}

function hideEmptySections(groupedProducts) {
    const productContainers = [
        'swiper_items_sale',
        'swiper_elctronics',
        'swiper_appliances',
        'swiper_mobiles'
    ];
    
    const categories = Object.keys(groupedProducts);
    
    productContainers.forEach((containerId, index) => {
        const container = document.getElementById(containerId);
        if (container) {
            const section = container.closest('.slide');
            if (index >= categories.length || !groupedProducts[categories[index]] || groupedProducts[categories[index]].length === 0) {
                section.style.display = 'none';
            }
        }
    });
}

function updateFilterStats() {
    const filterHeader = document.querySelector('.filter-header h2');
    if (filterHeader) {
        const totalProducts = allProducts.length;
        const filteredCount = filteredProducts.length;
        filterHeader.innerHTML = `<i class="fas fa-filter"></i> Filter Products (${filteredCount}/${totalProducts})`;
    }
}

function clearAllFilters() {
    // Reset filter values
    currentFilters = {
        categories: ['all'],
        priceRange: { min: 0, max: 1000 },
        sortBy: 'default'
    };
    
    // Reset UI elements
    const priceMinSlider = document.getElementById('price-min');
    const priceMaxSlider = document.getElementById('price-max');
    const priceMinValue = document.getElementById('price-min-value');
    const priceMaxValue = document.getElementById('price-max-value');
    const sortSelect = document.getElementById('sort-products');
    const categoryCheckboxes = document.querySelectorAll('.filter-option input[type="checkbox"]');
    
    if (priceMinSlider) {
        priceMinSlider.value = 0;
        priceMinValue.textContent = '0';
    }
    
    if (priceMaxSlider) {
        priceMaxSlider.value = 1000;
        priceMaxValue.textContent = '1000';
    }
    
    if (sortSelect) {
        sortSelect.value = 'default';
    }
    
    // Reset checkboxes
    categoryCheckboxes.forEach(checkbox => {
        if (checkbox.value === 'all') {
            checkbox.checked = true;
        } else {
            checkbox.checked = false;
        }
    });
    
    // Reset products display
    filteredProducts = [...allProducts];
    resetProductDisplay();
    updateFilterStats();
}

function resetProductDisplay() {
    // Show all sections
    const allSections = document.querySelectorAll('.slide');
    allSections.forEach(section => {
        section.style.display = 'block';
    });
    
    // Reload original products (this would need to be implemented based on your original loading logic)
    if (typeof loadOriginalProducts === 'function') {
        loadOriginalProducts();
    }
}

// Quick filter functions
function quickFilterByCategory(category) {
    currentFilters.categories = [category];
    
    // Update UI
    const categoryCheckboxes = document.querySelectorAll('.filter-option input[type="checkbox"]');
    categoryCheckboxes.forEach(checkbox => {
        checkbox.checked = checkbox.value === category;
    });
    
    applyFilters();
}

function quickFilterByPriceRange(min, max) {
    currentFilters.priceRange = { min, max };
    
    // Update UI
    const priceMinSlider = document.getElementById('price-min');
    const priceMaxSlider = document.getElementById('price-max');
    const priceMinValue = document.getElementById('price-min-value');
    const priceMaxValue = document.getElementById('price-max-value');
    
    if (priceMinSlider) {
        priceMinSlider.value = min;
        priceMinValue.textContent = min;
    }
    
    if (priceMaxSlider) {
        priceMaxSlider.value = max;
        priceMaxValue.textContent = max;
    }
    
    applyFilters();
}

// Export functions for global use
window.quickFilterByCategory = quickFilterByCategory;
window.quickFilterByPriceRange = quickFilterByPriceRange;
window.clearAllFilters = clearAllFilters;
